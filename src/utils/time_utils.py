"""
Time utilities for the funding rate arbitrage system.
"""

from datetime import datetime, timezone, timedelta
from typing import Optional

import pandas as pd


def get_next_funding_time(current_time: Optional[datetime] = None) -> datetime:
    """
    Get the next funding time for Binance perpetual futures.
    
    Binance funding occurs every 8 hours at 00:00, 08:00, and 16:00 UTC.
    
    Parameters
    ----------
    current_time : datetime, optional
        Current time. If None, uses current UTC time.
    
    Returns
    -------
    datetime
        Next funding time in UTC.
    """
    if current_time is None:
        current_time = datetime.now(timezone.utc)
    
    # Ensure timezone awareness
    if current_time.tzinfo is None:
        current_time = current_time.replace(tzinfo=timezone.utc)
    
    # Convert to UTC if not already
    current_time = current_time.astimezone(timezone.utc)
    
    # Funding times in UTC: 00:00, 08:00, 16:00
    funding_hours = [0, 8, 16]
    
    # Find next funding time
    current_hour = current_time.hour
    current_date = current_time.date()
    
    for hour in funding_hours:
        next_funding = datetime.combine(
            current_date, 
            datetime.min.time().replace(hour=hour),
            timezone.utc
        )
        
        if next_funding > current_time:
            return next_funding
    
    # If no funding time today, get first funding time tomorrow
    next_date = current_date + timedelta(days=1)
    return datetime.combine(
        next_date,
        datetime.min.time().replace(hour=funding_hours[0]),
        timezone.utc
    )


def time_to_funding(current_time: Optional[datetime] = None) -> timedelta:
    """
    Calculate time remaining until next funding.
    
    Parameters
    ----------
    current_time : datetime, optional
        Current time. If None, uses current UTC time.
    
    Returns
    -------
    timedelta
        Time remaining until next funding.
    """
    if current_time is None:
        current_time = datetime.now(timezone.utc)
    
    next_funding = get_next_funding_time(current_time)
    return next_funding - current_time


def get_previous_funding_time(current_time: Optional[datetime] = None) -> datetime:
    """
    Get the previous funding time.
    
    Parameters
    ----------
    current_time : datetime, optional
        Current time. If None, uses current UTC time.
    
    Returns
    -------
    datetime
        Previous funding time in UTC.
    """
    if current_time is None:
        current_time = datetime.now(timezone.utc)
    
    # Ensure timezone awareness
    if current_time.tzinfo is None:
        current_time = current_time.replace(tzinfo=timezone.utc)
    
    # Convert to UTC if not already
    current_time = current_time.astimezone(timezone.utc)
    
    # Funding times in UTC: 00:00, 08:00, 16:00
    funding_hours = [0, 8, 16]
    
    # Find previous funding time
    current_hour = current_time.hour
    current_date = current_time.date()
    
    # Check funding times in reverse order
    for hour in reversed(funding_hours):
        prev_funding = datetime.combine(
            current_date,
            datetime.min.time().replace(hour=hour),
            timezone.utc
        )
        
        if prev_funding <= current_time:
            return prev_funding
    
    # If no funding time today, get last funding time yesterday
    prev_date = current_date - timedelta(days=1)
    return datetime.combine(
        prev_date,
        datetime.min.time().replace(hour=funding_hours[-1]),
        timezone.utc
    )


def get_funding_times_in_range(
    start_time: datetime,
    end_time: datetime,
) -> list[datetime]:
    """
    Get all funding times within a date range.
    
    Parameters
    ----------
    start_time : datetime
        Start of the range.
    end_time : datetime
        End of the range.
    
    Returns
    -------
    list[datetime]
        List of funding times within the range.
    """
    # Ensure timezone awareness
    if start_time.tzinfo is None:
        start_time = start_time.replace(tzinfo=timezone.utc)
    if end_time.tzinfo is None:
        end_time = end_time.replace(tzinfo=timezone.utc)
    
    # Convert to UTC
    start_time = start_time.astimezone(timezone.utc)
    end_time = end_time.astimezone(timezone.utc)
    
    funding_times = []
    funding_hours = [0, 8, 16]
    
    current_date = start_time.date()
    end_date = end_time.date()
    
    while current_date <= end_date:
        for hour in funding_hours:
            funding_time = datetime.combine(
                current_date,
                datetime.min.time().replace(hour=hour),
                timezone.utc
            )
            
            if start_time <= funding_time <= end_time:
                funding_times.append(funding_time)
        
        current_date += timedelta(days=1)
    
    return funding_times


def is_near_funding_time(
    current_time: Optional[datetime] = None,
    threshold_minutes: int = 30,
) -> bool:
    """
    Check if current time is near a funding time.
    
    Parameters
    ----------
    current_time : datetime, optional
        Current time. If None, uses current UTC time.
    threshold_minutes : int, default 30
        Threshold in minutes to consider "near" funding time.
    
    Returns
    -------
    bool
        True if near funding time.
    """
    time_remaining = time_to_funding(current_time)
    threshold = timedelta(minutes=threshold_minutes)
    
    return time_remaining <= threshold


def get_funding_period_start(current_time: Optional[datetime] = None) -> datetime:
    """
    Get the start time of the current funding period.
    
    Parameters
    ----------
    current_time : datetime, optional
        Current time. If None, uses current UTC time.
    
    Returns
    -------
    datetime
        Start time of current funding period.
    """
    return get_previous_funding_time(current_time)


def calculate_funding_periods_elapsed(
    start_time: datetime,
    end_time: Optional[datetime] = None,
) -> int:
    """
    Calculate number of funding periods elapsed between two times.
    
    Parameters
    ----------
    start_time : datetime
        Start time.
    end_time : datetime, optional
        End time. If None, uses current time.
    
    Returns
    -------
    int
        Number of funding periods elapsed.
    """
    if end_time is None:
        end_time = datetime.now(timezone.utc)
    
    funding_times = get_funding_times_in_range(start_time, end_time)
    return len(funding_times)


def format_time_to_funding(current_time: Optional[datetime] = None) -> str:
    """
    Format time remaining to next funding as a human-readable string.
    
    Parameters
    ----------
    current_time : datetime, optional
        Current time. If None, uses current UTC time.
    
    Returns
    -------
    str
        Formatted time string (e.g., "2h 30m").
    """
    time_remaining = time_to_funding(current_time)
    
    hours = time_remaining.seconds // 3600
    minutes = (time_remaining.seconds % 3600) // 60
    
    if hours > 0:
        return f"{hours}h {minutes}m"
    else:
        return f"{minutes}m"


def get_market_hours_info(current_time: Optional[datetime] = None) -> dict:
    """
    Get information about market hours and funding schedule.
    
    Parameters
    ----------
    current_time : datetime, optional
        Current time. If None, uses current UTC time.
    
    Returns
    -------
    dict
        Market hours information.
    """
    if current_time is None:
        current_time = datetime.now(timezone.utc)
    
    next_funding = get_next_funding_time(current_time)
    prev_funding = get_previous_funding_time(current_time)
    time_to_next = time_to_funding(current_time)
    
    return {
        "current_time": current_time,
        "next_funding_time": next_funding,
        "previous_funding_time": prev_funding,
        "time_to_next_funding": time_to_next,
        "time_to_next_funding_str": format_time_to_funding(current_time),
        "is_near_funding": is_near_funding_time(current_time),
        "funding_period_start": get_funding_period_start(current_time),
    }

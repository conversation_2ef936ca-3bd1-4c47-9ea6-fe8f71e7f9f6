"""
Calculation utilities for the funding rate arbitrage system.
"""

from decimal import Decimal
from typing import Optional, Tuple

import pandas as pd


def calculate_funding_rate_profit(
    funding_rate: Decimal,
    position_size: Decimal,
    mark_price: Decimal,
    holding_periods: int = 1,
) -> Decimal:
    """
    Calculate expected profit from funding rate arbitrage.
    
    Parameters
    ----------
    funding_rate : Decimal
        The funding rate (as a decimal, e.g., 0.0001 for 0.01%).
    position_size : Decimal
        Position size in base currency.
    mark_price : Decimal
        Mark price of the futures contract.
    holding_periods : int, default 1
        Number of funding periods to hold (funding occurs every 8 hours).
    
    Returns
    -------
    Decimal
        Expected profit from funding rate arbitrage.
    """
    notional_value = position_size * mark_price
    profit = notional_value * funding_rate * Decimal(str(holding_periods))
    return profit


def calculate_hedge_ratio(
    spot_price: Decimal,
    futures_price: Decimal,
    spot_volatility: Optional[Decimal] = None,
    futures_volatility: Optional[Decimal] = None,
    correlation: Optional[Decimal] = None,
) -> Decimal:
    """
    Calculate optimal hedge ratio for spot-futures arbitrage.
    
    Parameters
    ----------
    spot_price : Decimal
        Current spot price.
    futures_price : Decimal
        Current futures price.
    spot_volatility : Decimal, optional
        Spot price volatility (if available for optimal hedging).
    futures_volatility : Decimal, optional
        Futures price volatility (if available for optimal hedging).
    correlation : Decimal, optional
        Correlation between spot and futures (if available for optimal hedging).
    
    Returns
    -------
    Decimal
        Hedge ratio (typically close to 1.0 for funding rate arbitrage).
    """
    # For funding rate arbitrage, we typically want a 1:1 hedge ratio
    # to maintain market neutrality
    if spot_volatility and futures_volatility and correlation:
        # Optimal hedge ratio using minimum variance approach
        hedge_ratio = correlation * (spot_volatility / futures_volatility)
        return hedge_ratio
    else:
        # Simple price-based hedge ratio
        return spot_price / futures_price


def calculate_position_size(
    available_capital: Decimal,
    risk_percentage: Decimal,
    price: Decimal,
    leverage: Decimal = Decimal("1.0"),
    max_position_size: Optional[Decimal] = None,
) -> Decimal:
    """
    Calculate position size based on risk management parameters.
    
    Parameters
    ----------
    available_capital : Decimal
        Available capital for trading.
    risk_percentage : Decimal
        Percentage of capital to risk (as decimal, e.g., 0.1 for 10%).
    price : Decimal
        Price per unit of the asset.
    leverage : Decimal, default 1.0
        Leverage to use.
    max_position_size : Decimal, optional
        Maximum allowed position size.
    
    Returns
    -------
    Decimal
        Calculated position size in base currency units.
    """
    # Calculate risk amount
    risk_amount = available_capital * risk_percentage
    
    # Calculate position size with leverage
    position_value = risk_amount * leverage
    position_size = position_value / price
    
    # Apply maximum position size limit
    if max_position_size and position_size > max_position_size:
        position_size = max_position_size
    
    return position_size


def calculate_arbitrage_profit(
    spot_price: Decimal,
    futures_price: Decimal,
    funding_rate: Decimal,
    position_size: Decimal,
    transaction_costs: Decimal = Decimal("0.001"),  # 0.1% default
    holding_periods: int = 1,
) -> Tuple[Decimal, Decimal, Decimal]:
    """
    Calculate total arbitrage profit including funding and price convergence.
    
    Parameters
    ----------
    spot_price : Decimal
        Spot price.
    futures_price : Decimal
        Futures price.
    funding_rate : Decimal
        Funding rate.
    position_size : Decimal
        Position size.
    transaction_costs : Decimal, default 0.001
        Transaction costs as percentage of notional.
    holding_periods : int, default 1
        Number of funding periods.
    
    Returns
    -------
    Tuple[Decimal, Decimal, Decimal]
        (funding_profit, convergence_profit, total_profit)
    """
    # Calculate funding profit
    notional_value = position_size * futures_price
    funding_profit = notional_value * funding_rate * Decimal(str(holding_periods))
    
    # Calculate convergence profit (assuming prices converge)
    price_spread = futures_price - spot_price
    convergence_profit = position_size * price_spread
    
    # Calculate transaction costs
    total_costs = notional_value * transaction_costs * Decimal("2")  # Entry and exit
    
    # Total profit
    total_profit = funding_profit + convergence_profit - total_costs
    
    return funding_profit, convergence_profit, total_profit


def calculate_break_even_funding_rate(
    spot_price: Decimal,
    futures_price: Decimal,
    transaction_costs: Decimal = Decimal("0.001"),
    holding_periods: int = 1,
) -> Decimal:
    """
    Calculate break-even funding rate for arbitrage profitability.
    
    Parameters
    ----------
    spot_price : Decimal
        Spot price.
    futures_price : Decimal
        Futures price.
    transaction_costs : Decimal, default 0.001
        Transaction costs as percentage of notional.
    holding_periods : int, default 1
        Number of funding periods.
    
    Returns
    -------
    Decimal
        Break-even funding rate.
    """
    # Price spread cost (if futures > spot, this is a cost for long arbitrage)
    price_spread = futures_price - spot_price
    spread_cost_rate = price_spread / futures_price
    
    # Transaction cost rate
    transaction_cost_rate = transaction_costs * Decimal("2")  # Entry and exit
    
    # Break-even funding rate
    break_even_rate = (spread_cost_rate + transaction_cost_rate) / Decimal(str(holding_periods))
    
    return break_even_rate


def calculate_sharpe_ratio(
    returns: pd.Series,
    risk_free_rate: Decimal = Decimal("0.02"),  # 2% annual
) -> Decimal:
    """
    Calculate Sharpe ratio for a return series.
    
    Parameters
    ----------
    returns : pd.Series
        Series of returns.
    risk_free_rate : Decimal, default 0.02
        Annual risk-free rate.
    
    Returns
    -------
    Decimal
        Sharpe ratio.
    """
    if len(returns) == 0 or returns.std() == 0:
        return Decimal("0")
    
    # Convert to annual terms
    annual_return = Decimal(str(returns.mean() * 252))  # Assuming daily returns
    annual_volatility = Decimal(str(returns.std() * (252 ** 0.5)))
    
    sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility
    return sharpe_ratio


def calculate_max_drawdown(equity_curve: pd.Series) -> Tuple[Decimal, pd.Timestamp, pd.Timestamp]:
    """
    Calculate maximum drawdown from an equity curve.
    
    Parameters
    ----------
    equity_curve : pd.Series
        Equity curve with timestamps as index.
    
    Returns
    -------
    Tuple[Decimal, pd.Timestamp, pd.Timestamp]
        (max_drawdown, start_date, end_date)
    """
    if len(equity_curve) == 0:
        return Decimal("0"), None, None
    
    # Calculate running maximum
    running_max = equity_curve.expanding().max()
    
    # Calculate drawdown
    drawdown = (equity_curve - running_max) / running_max
    
    # Find maximum drawdown
    max_drawdown_idx = drawdown.idxmin()
    max_drawdown_value = Decimal(str(abs(drawdown.min())))
    
    # Find start of drawdown period
    start_idx = running_max.loc[:max_drawdown_idx].idxmax()
    
    return max_drawdown_value, start_idx, max_drawdown_idx


def calculate_volatility(
    prices: pd.Series,
    window: int = 20,
    annualize: bool = True,
) -> pd.Series:
    """
    Calculate rolling volatility of price series.
    
    Parameters
    ----------
    prices : pd.Series
        Price series.
    window : int, default 20
        Rolling window size.
    annualize : bool, default True
        Whether to annualize the volatility.
    
    Returns
    -------
    pd.Series
        Rolling volatility series.
    """
    returns = prices.pct_change().dropna()
    volatility = returns.rolling(window=window).std()
    
    if annualize:
        volatility = volatility * (252 ** 0.5)  # Assuming daily data
    
    return volatility

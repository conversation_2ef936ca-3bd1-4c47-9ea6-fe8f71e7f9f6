"""
Logging utilities for the funding rate arbitrage system.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

import structlog


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: str = "10MB",
    backup_count: int = 5,
    format_string: Optional[str] = None,
    use_structured: bool = True,
) -> logging.Logger:
    """
    Set up logging configuration for the application.
    
    Parameters
    ----------
    level : str, default "INFO"
        Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL).
    log_file : str, optional
        Path to log file. If None, logs only to console.
    max_file_size : str, default "10MB"
        Maximum log file size before rotation.
    backup_count : int, default 5
        Number of backup files to keep.
    format_string : str, optional
        Custom format string for log messages.
    use_structured : bool, default True
        Whether to use structured logging with structlog.
    
    Returns
    -------
    logging.Logger
        Configured logger instance.
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Default format string
    if format_string is None:
        format_string = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(filename)s:%(lineno)d - %(message)s"
        )
    
    # Create formatter
    formatter = logging.Formatter(format_string)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Parse file size
        size_bytes = _parse_file_size(max_file_size)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=size_bytes,
            backupCount=backup_count,
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Configure structured logging if requested
    if use_structured:
        _setup_structlog(numeric_level)
    
    # Create application logger
    logger = logging.getLogger("funding_arbitrage")
    
    return logger


def _parse_file_size(size_str: str) -> int:
    """
    Parse file size string to bytes.
    
    Parameters
    ----------
    size_str : str
        Size string (e.g., "10MB", "1GB").
    
    Returns
    -------
    int
        Size in bytes.
    """
    size_str = size_str.upper().strip()
    
    if size_str.endswith("KB"):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith("MB"):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith("GB"):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        # Assume bytes
        return int(size_str)


def _setup_structlog(level: int) -> None:
    """
    Configure structured logging with structlog.
    
    Parameters
    ----------
    level : int
        Logging level.
    """
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Parameters
    ----------
    name : str
        Logger name.
    
    Returns
    -------
    structlog.BoundLogger
        Structured logger instance.
    """
    return structlog.get_logger(name)


class TradingLogger:
    """
    Specialized logger for trading operations.
    """
    
    def __init__(self, name: str = "trading") -> None:
        self.logger = get_logger(name)
    
    def log_trade_signal(
        self,
        symbol: str,
        signal_type: str,
        funding_rate: float,
        spread: float,
        confidence: float,
        **kwargs,
    ) -> None:
        """Log a trading signal."""
        self.logger.info(
            "Trade signal generated",
            symbol=symbol,
            signal_type=signal_type,
            funding_rate=funding_rate,
            spread=spread,
            confidence=confidence,
            **kwargs,
        )
    
    def log_order_event(
        self,
        symbol: str,
        order_type: str,
        side: str,
        quantity: float,
        price: float,
        status: str,
        **kwargs,
    ) -> None:
        """Log an order event."""
        self.logger.info(
            "Order event",
            symbol=symbol,
            order_type=order_type,
            side=side,
            quantity=quantity,
            price=price,
            status=status,
            **kwargs,
        )
    
    def log_position_update(
        self,
        symbol: str,
        side: str,
        size: float,
        entry_price: float,
        current_price: float,
        pnl: float,
        **kwargs,
    ) -> None:
        """Log a position update."""
        self.logger.info(
            "Position update",
            symbol=symbol,
            side=side,
            size=size,
            entry_price=entry_price,
            current_price=current_price,
            pnl=pnl,
            **kwargs,
        )
    
    def log_funding_payment(
        self,
        symbol: str,
        funding_rate: float,
        position_size: float,
        payment: float,
        **kwargs,
    ) -> None:
        """Log a funding payment."""
        self.logger.info(
            "Funding payment",
            symbol=symbol,
            funding_rate=funding_rate,
            position_size=position_size,
            payment=payment,
            **kwargs,
        )
    
    def log_risk_event(
        self,
        event_type: str,
        symbol: str,
        risk_metric: str,
        current_value: float,
        threshold: float,
        action: str,
        **kwargs,
    ) -> None:
        """Log a risk management event."""
        self.logger.warning(
            "Risk event",
            event_type=event_type,
            symbol=symbol,
            risk_metric=risk_metric,
            current_value=current_value,
            threshold=threshold,
            action=action,
            **kwargs,
        )
    
    def log_error(
        self,
        error_type: str,
        message: str,
        symbol: Optional[str] = None,
        **kwargs,
    ) -> None:
        """Log an error."""
        self.logger.error(
            "System error",
            error_type=error_type,
            message=message,
            symbol=symbol,
            **kwargs,
        )
    
    def log_performance_metrics(
        self,
        period: str,
        total_pnl: float,
        funding_pnl: float,
        trading_pnl: float,
        sharpe_ratio: float,
        max_drawdown: float,
        **kwargs,
    ) -> None:
        """Log performance metrics."""
        self.logger.info(
            "Performance metrics",
            period=period,
            total_pnl=total_pnl,
            funding_pnl=funding_pnl,
            trading_pnl=trading_pnl,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            **kwargs,
        )

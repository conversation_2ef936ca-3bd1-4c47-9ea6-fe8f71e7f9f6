"""
Position sizing utilities for the funding rate arbitrage system.

This module provides position sizing algorithms based on various risk management
approaches including fixed size, percentage of capital, and volatility-based sizing.
"""

from decimal import Decimal
from typing import Optional, Dict, List
from enum import Enum

import pandas as pd
from nautilus_trader.model.objects import Money, Quantity
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.portfolio.portfolio import Portfolio

from src.utils.calculations import calculate_volatility
from src.utils.logging import TradingLogger


class PositionSizingMethod(Enum):
    """Position sizing methods."""
    FIXED = "fixed"
    PERCENT_CAPITAL = "percent_capital"
    VOLATILITY_TARGET = "volatility_target"
    KELLY_CRITERION = "kelly_criterion"
    RISK_PARITY = "risk_parity"


class PositionSizer:
    """
    Position sizing calculator for the arbitrage system.
    """
    
    def __init__(
        self,
        portfolio: Portfolio,
        default_method: PositionSizingMethod = PositionSizingMethod.PERCENT_CAPITAL,
        default_size: Decimal = Decimal("0.1"),  # 10% of capital
        max_position_size: Decimal = Decimal("1000.0"),
        volatility_target: Decimal = Decimal("0.02"),  # 2% daily volatility target
        volatility_lookback: int = 20,  # 20 days
    ) -> None:
        self._portfolio = portfolio
        self._default_method = default_method
        self._default_size = default_size
        self._max_position_size = max_position_size
        self._volatility_target = volatility_target
        self._volatility_lookback = volatility_lookback
        
        # Method-specific configurations
        self._method_configs: Dict[str, Dict] = {}
        
        # Price history for volatility calculations
        self._price_history: Dict[str, List[Decimal]] = {}
        
        # Logger
        self._logger = TradingLogger("position_sizer")
    
    def set_method_config(
        self,
        symbol: str,
        method: PositionSizingMethod,
        **kwargs,
    ) -> None:
        """Set position sizing method and parameters for a specific symbol."""
        self._method_configs[symbol] = {
            "method": method,
            **kwargs,
        }
        
        self._logger.logger.info(
            f"Set position sizing method for {symbol}: {method.value}",
            extra=kwargs,
        )
    
    def update_price_history(self, symbol: str, price: Decimal) -> None:
        """Update price history for volatility calculations."""
        if symbol not in self._price_history:
            self._price_history[symbol] = []
        
        self._price_history[symbol].append(price)
        
        # Keep only recent history
        max_history = self._volatility_lookback * 2  # Buffer for calculations
        if len(self._price_history[symbol]) > max_history:
            self._price_history[symbol] = self._price_history[symbol][-max_history:]
    
    def calculate_position_size(
        self,
        symbol: str,
        current_price: Decimal,
        signal_strength: Optional[Decimal] = None,
        expected_return: Optional[Decimal] = None,
        expected_volatility: Optional[Decimal] = None,
    ) -> Decimal:
        """
        Calculate position size for a symbol.
        
        Parameters
        ----------
        symbol : str
            The symbol to calculate position size for.
        current_price : Decimal
            Current price of the instrument.
        signal_strength : Decimal, optional
            Signal strength (0-1) for scaling position size.
        expected_return : Decimal, optional
            Expected return for Kelly criterion.
        expected_volatility : Decimal, optional
            Expected volatility for volatility targeting.
        
        Returns
        -------
        Decimal
            Calculated position size in base currency units.
        """
        # Get method configuration
        config = self._method_configs.get(symbol, {
            "method": self._default_method,
            "size": self._default_size,
        })
        
        method = config["method"]
        
        # Calculate base position size
        if method == PositionSizingMethod.FIXED:
            position_size = self._calculate_fixed_size(config)
        elif method == PositionSizingMethod.PERCENT_CAPITAL:
            position_size = self._calculate_percent_capital_size(config, current_price)
        elif method == PositionSizingMethod.VOLATILITY_TARGET:
            position_size = self._calculate_volatility_target_size(
                symbol, current_price, expected_volatility
            )
        elif method == PositionSizingMethod.KELLY_CRITERION:
            position_size = self._calculate_kelly_size(
                symbol, current_price, expected_return, expected_volatility
            )
        elif method == PositionSizingMethod.RISK_PARITY:
            position_size = self._calculate_risk_parity_size(
                symbol, current_price, expected_volatility
            )
        else:
            # Default to percent capital
            position_size = self._calculate_percent_capital_size(
                {"size": self._default_size}, current_price
            )
        
        # Apply signal strength scaling
        if signal_strength is not None:
            position_size = position_size * signal_strength
        
        # Apply maximum position size limit
        if position_size > self._max_position_size:
            position_size = self._max_position_size
        
        # Ensure minimum viable position size
        min_size = Decimal("0.001")  # Minimum 0.001 units
        if position_size < min_size:
            position_size = Decimal("0")
        
        return position_size
    
    def _calculate_fixed_size(self, config: Dict) -> Decimal:
        """Calculate fixed position size."""
        return config.get("size", self._default_size)
    
    def _calculate_percent_capital_size(self, config: Dict, current_price: Decimal) -> Decimal:
        """Calculate position size as percentage of available capital."""
        percentage = config.get("size", self._default_size)
        
        # Get available balance
        total_balance = self._get_total_balance()
        
        if total_balance <= 0:
            return Decimal("0")
        
        # Calculate position value
        position_value = total_balance * percentage
        
        # Convert to position size
        position_size = position_value / current_price
        
        return position_size
    
    def _calculate_volatility_target_size(
        self,
        symbol: str,
        current_price: Decimal,
        expected_volatility: Optional[Decimal] = None,
    ) -> Decimal:
        """Calculate position size based on volatility targeting."""
        # Get or calculate volatility
        if expected_volatility is None:
            volatility = self._calculate_historical_volatility(symbol)
        else:
            volatility = expected_volatility
        
        if volatility <= 0:
            # Fallback to percent capital method
            return self._calculate_percent_capital_size(
                {"size": self._default_size}, current_price
            )
        
        # Get available balance
        total_balance = self._get_total_balance()
        
        if total_balance <= 0:
            return Decimal("0")
        
        # Calculate position size to achieve target volatility
        # position_size = (target_vol * capital) / (price * asset_vol)
        position_value = (self._volatility_target * total_balance) / volatility
        position_size = position_value / current_price
        
        return position_size
    
    def _calculate_kelly_size(
        self,
        symbol: str,
        current_price: Decimal,
        expected_return: Optional[Decimal] = None,
        expected_volatility: Optional[Decimal] = None,
    ) -> Decimal:
        """Calculate position size using Kelly criterion."""
        if expected_return is None or expected_volatility is None:
            # Fallback to percent capital method
            return self._calculate_percent_capital_size(
                {"size": self._default_size}, current_price
            )
        
        if expected_volatility <= 0:
            return Decimal("0")
        
        # Kelly fraction = expected_return / variance
        variance = expected_volatility ** 2
        kelly_fraction = expected_return / variance
        
        # Apply Kelly fraction cap (typically 25% max)
        max_kelly = Decimal("0.25")
        kelly_fraction = min(kelly_fraction, max_kelly)
        kelly_fraction = max(kelly_fraction, Decimal("0"))  # No negative positions
        
        # Get available balance
        total_balance = self._get_total_balance()
        
        if total_balance <= 0:
            return Decimal("0")
        
        # Calculate position size
        position_value = total_balance * kelly_fraction
        position_size = position_value / current_price
        
        return position_size
    
    def _calculate_risk_parity_size(
        self,
        symbol: str,
        current_price: Decimal,
        expected_volatility: Optional[Decimal] = None,
    ) -> Decimal:
        """Calculate position size using risk parity approach."""
        # Get or calculate volatility
        if expected_volatility is None:
            volatility = self._calculate_historical_volatility(symbol)
        else:
            volatility = expected_volatility
        
        if volatility <= 0:
            # Fallback to percent capital method
            return self._calculate_percent_capital_size(
                {"size": self._default_size}, current_price
            )
        
        # Get available balance
        total_balance = self._get_total_balance()
        
        if total_balance <= 0:
            return Decimal("0")
        
        # Risk parity: allocate equal risk to each position
        # Assuming equal risk allocation across all instruments
        risk_allocation = self._default_size  # Use as risk budget percentage
        
        # Position size = (risk_budget * capital) / (price * volatility)
        position_value = (risk_allocation * total_balance) / volatility
        position_size = position_value / current_price
        
        return position_size
    
    def _calculate_historical_volatility(self, symbol: str) -> Decimal:
        """Calculate historical volatility for a symbol."""
        if symbol not in self._price_history:
            return self._volatility_target  # Default volatility
        
        prices = self._price_history[symbol]
        
        if len(prices) < 2:
            return self._volatility_target
        
        # Convert to pandas series for calculation
        price_series = pd.Series([float(p) for p in prices])
        
        # Calculate volatility
        volatility_series = calculate_volatility(
            price_series,
            window=min(self._volatility_lookback, len(prices) - 1),
            annualize=False,  # Daily volatility
        )
        
        if volatility_series.empty:
            return self._volatility_target
        
        # Get latest volatility
        latest_volatility = Decimal(str(volatility_series.iloc[-1]))
        
        return latest_volatility
    
    def _get_total_balance(self) -> Decimal:
        """Get total available balance across all accounts."""
        total_balance = Decimal("0")
        
        # Sum balances across all accounts
        for account in self._portfolio.accounts():
            balance = account.balance_total()
            if balance:
                total_balance += Decimal(str(balance.as_decimal()))
        
        return total_balance
    
    def get_sizing_summary(self) -> Dict:
        """
        Get position sizing configuration summary.
        
        Returns
        -------
        Dict
            Position sizing summary.
        """
        return {
            "default_method": self._default_method.value,
            "default_size": float(self._default_size),
            "max_position_size": float(self._max_position_size),
            "volatility_target": float(self._volatility_target),
            "volatility_lookback": self._volatility_lookback,
            "configured_symbols": list(self._method_configs.keys()),
            "total_balance": float(self._get_total_balance()),
        }

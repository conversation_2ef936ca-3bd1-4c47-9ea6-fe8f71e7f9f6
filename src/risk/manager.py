"""
Risk management system for the funding rate arbitrage strategy.

This module provides comprehensive risk management including position sizing,
exposure limits, and emergency stop mechanisms.
"""

from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, NamedTuple
from enum import Enum

import pandas as pd
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.objects import Money, Quantity
from nautilus_trader.model.position import Position
from nautilus_trader.portfolio.portfolio import Portfolio

from src.utils.logging import TradingLogger


class RiskLevel(Enum):
    """Risk level indicators."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskEvent(NamedTuple):
    """Risk event notification."""
    event_type: str
    risk_level: RiskLevel
    symbol: str
    metric: str
    current_value: Decimal
    threshold: Decimal
    message: str
    timestamp: datetime


class RiskMetrics(NamedTuple):
    """Risk metrics for a symbol or portfolio."""
    symbol: Optional[str]
    position_size: Decimal
    exposure: Decimal
    leverage: Decimal
    var_1d: Decimal  # 1-day Value at Risk
    max_drawdown: Decimal
    sharpe_ratio: Decimal
    risk_level: RiskLevel


class RiskManager:
    """
    Comprehensive risk management system.
    """
    
    def __init__(
        self,
        portfolio: Portfolio,
        max_position_size: Decimal = Decimal("1000.0"),
        max_portfolio_exposure: Decimal = Decimal("10000.0"),
        max_leverage: Decimal = Decimal("3.0"),
        stop_loss_percentage: Decimal = Decimal("0.05"),
        var_confidence: Decimal = Decimal("0.95"),
        max_drawdown_threshold: Decimal = Decimal("0.10"),
        min_sharpe_ratio: Decimal = Decimal("0.5"),
    ) -> None:
        self._portfolio = portfolio
        self._max_position_size = max_position_size
        self._max_portfolio_exposure = max_portfolio_exposure
        self._max_leverage = max_leverage
        self._stop_loss_percentage = stop_loss_percentage
        self._var_confidence = var_confidence
        self._max_drawdown_threshold = max_drawdown_threshold
        self._min_sharpe_ratio = min_sharpe_ratio
        
        # Risk tracking
        self._risk_events: List[RiskEvent] = []
        self._position_limits: Dict[str, Decimal] = {}
        self._emergency_stop_triggered = False
        
        # Performance tracking
        self._equity_curve: List[tuple[datetime, Decimal]] = []
        self._returns_history: List[Decimal] = []
        
        # Logger
        self._logger = TradingLogger("risk_manager")
    
    def set_position_limit(self, symbol: str, limit: Decimal) -> None:
        """Set position limit for a specific symbol."""
        self._position_limits[symbol] = limit
        self._logger.logger.info(f"Set position limit for {symbol}: {limit}")
    
    def check_position_risk(
        self,
        symbol: str,
        proposed_size: Decimal,
        current_price: Decimal,
    ) -> tuple[bool, Optional[RiskEvent]]:
        """
        Check if a proposed position size is within risk limits.
        
        Parameters
        ----------
        symbol : str
            The symbol to check.
        proposed_size : Decimal
            Proposed position size.
        current_price : Decimal
            Current price of the instrument.
        
        Returns
        -------
        tuple[bool, Optional[RiskEvent]]
            (is_allowed, risk_event)
        """
        # Check individual position size limit
        symbol_limit = self._position_limits.get(symbol, self._max_position_size)
        if abs(proposed_size) > symbol_limit:
            event = RiskEvent(
                event_type="position_size_exceeded",
                risk_level=RiskLevel.HIGH,
                symbol=symbol,
                metric="position_size",
                current_value=abs(proposed_size),
                threshold=symbol_limit,
                message=f"Proposed position size {proposed_size} exceeds limit {symbol_limit}",
                timestamp=datetime.now(timezone.utc),
            )
            return False, event
        
        # Check portfolio exposure
        proposed_exposure = abs(proposed_size) * current_price
        current_exposure = self._calculate_portfolio_exposure()
        total_exposure = current_exposure + proposed_exposure
        
        if total_exposure > self._max_portfolio_exposure:
            event = RiskEvent(
                event_type="portfolio_exposure_exceeded",
                risk_level=RiskLevel.HIGH,
                symbol=symbol,
                metric="portfolio_exposure",
                current_value=total_exposure,
                threshold=self._max_portfolio_exposure,
                message=f"Total exposure {total_exposure} would exceed limit {self._max_portfolio_exposure}",
                timestamp=datetime.now(timezone.utc),
            )
            return False, event
        
        # Check leverage
        account_balance = self._get_account_balance()
        if account_balance > 0:
            leverage = total_exposure / account_balance
            if leverage > self._max_leverage:
                event = RiskEvent(
                    event_type="leverage_exceeded",
                    risk_level=RiskLevel.MEDIUM,
                    symbol=symbol,
                    metric="leverage",
                    current_value=leverage,
                    threshold=self._max_leverage,
                    message=f"Leverage {leverage} would exceed limit {self._max_leverage}",
                    timestamp=datetime.now(timezone.utc),
                )
                return False, event
        
        return True, None
    
    def check_stop_loss(self, symbol: str, entry_price: Decimal, current_price: Decimal, side: str) -> bool:
        """
        Check if stop loss should be triggered.
        
        Parameters
        ----------
        symbol : str
            The symbol to check.
        entry_price : Decimal
            Entry price of the position.
        current_price : Decimal
            Current market price.
        side : str
            Position side ("long" or "short").
        
        Returns
        -------
        bool
            True if stop loss should be triggered.
        """
        if side.lower() == "long":
            # Long position: stop loss if price falls below threshold
            stop_price = entry_price * (Decimal("1") - self._stop_loss_percentage)
            return current_price <= stop_price
        elif side.lower() == "short":
            # Short position: stop loss if price rises above threshold
            stop_price = entry_price * (Decimal("1") + self._stop_loss_percentage)
            return current_price >= stop_price
        
        return False
    
    def update_equity_curve(self, timestamp: datetime, equity: Decimal) -> None:
        """Update equity curve for risk monitoring."""
        self._equity_curve.append((timestamp, equity))
        
        # Calculate returns if we have previous data
        if len(self._equity_curve) > 1:
            prev_equity = self._equity_curve[-2][1]
            if prev_equity > 0:
                return_pct = (equity - prev_equity) / prev_equity
                self._returns_history.append(return_pct)
                
                # Keep only recent returns (e.g., last 252 days)
                if len(self._returns_history) > 252:
                    self._returns_history = self._returns_history[-252:]
    
    def calculate_var(self, confidence: Optional[Decimal] = None) -> Decimal:
        """
        Calculate Value at Risk (VaR).
        
        Parameters
        ----------
        confidence : Decimal, optional
            Confidence level (default: 0.95).
        
        Returns
        -------
        Decimal
            VaR as a percentage.
        """
        if not self._returns_history:
            return Decimal("0")
        
        confidence = confidence or self._var_confidence
        returns_series = pd.Series([float(r) for r in self._returns_history])
        
        # Calculate VaR using historical simulation
        var_percentile = (1 - confidence) * 100
        var = returns_series.quantile(var_percentile / 100)
        
        return Decimal(str(abs(var)))
    
    def calculate_max_drawdown(self) -> Decimal:
        """
        Calculate maximum drawdown from equity curve.
        
        Returns
        -------
        Decimal
            Maximum drawdown as a percentage.
        """
        if len(self._equity_curve) < 2:
            return Decimal("0")
        
        equity_values = [float(eq[1]) for eq in self._equity_curve]
        equity_series = pd.Series(equity_values)
        
        # Calculate running maximum
        running_max = equity_series.expanding().max()
        
        # Calculate drawdown
        drawdown = (equity_series - running_max) / running_max
        
        # Return maximum drawdown as positive percentage
        max_drawdown = abs(drawdown.min())
        return Decimal(str(max_drawdown))
    
    def calculate_sharpe_ratio(self, risk_free_rate: Decimal = Decimal("0.02")) -> Decimal:
        """
        Calculate Sharpe ratio.
        
        Parameters
        ----------
        risk_free_rate : Decimal, default 0.02
            Annual risk-free rate.
        
        Returns
        -------
        Decimal
            Sharpe ratio.
        """
        if not self._returns_history:
            return Decimal("0")
        
        returns_series = pd.Series([float(r) for r in self._returns_history])
        
        if returns_series.std() == 0:
            return Decimal("0")
        
        # Annualize returns and volatility (assuming daily data)
        annual_return = returns_series.mean() * 252
        annual_volatility = returns_series.std() * (252 ** 0.5)
        
        sharpe = (annual_return - float(risk_free_rate)) / annual_volatility
        return Decimal(str(sharpe))
    
    def get_risk_metrics(self, symbol: Optional[str] = None) -> RiskMetrics:
        """
        Get risk metrics for a symbol or the entire portfolio.
        
        Parameters
        ----------
        symbol : str, optional
            Symbol to get metrics for. If None, returns portfolio metrics.
        
        Returns
        -------
        RiskMetrics
            Risk metrics.
        """
        if symbol:
            # Symbol-specific metrics
            position_size = self._get_position_size(symbol)
            exposure = self._get_position_exposure(symbol)
        else:
            # Portfolio metrics
            position_size = Decimal("0")  # Not applicable for portfolio
            exposure = self._calculate_portfolio_exposure()
        
        account_balance = self._get_account_balance()
        leverage = exposure / account_balance if account_balance > 0 else Decimal("0")
        
        var_1d = self.calculate_var()
        max_drawdown = self.calculate_max_drawdown()
        sharpe_ratio = self.calculate_sharpe_ratio()
        
        # Determine risk level
        risk_level = self._assess_risk_level(leverage, var_1d, max_drawdown, sharpe_ratio)
        
        return RiskMetrics(
            symbol=symbol,
            position_size=position_size,
            exposure=exposure,
            leverage=leverage,
            var_1d=var_1d,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            risk_level=risk_level,
        )
    
    def _assess_risk_level(
        self,
        leverage: Decimal,
        var_1d: Decimal,
        max_drawdown: Decimal,
        sharpe_ratio: Decimal,
    ) -> RiskLevel:
        """Assess overall risk level based on metrics."""
        risk_score = 0
        
        # Leverage risk
        if leverage > self._max_leverage * Decimal("0.9"):
            risk_score += 3
        elif leverage > self._max_leverage * Decimal("0.7"):
            risk_score += 2
        elif leverage > self._max_leverage * Decimal("0.5"):
            risk_score += 1
        
        # VaR risk
        if var_1d > Decimal("0.05"):  # 5% daily VaR
            risk_score += 3
        elif var_1d > Decimal("0.03"):  # 3% daily VaR
            risk_score += 2
        elif var_1d > Decimal("0.02"):  # 2% daily VaR
            risk_score += 1
        
        # Drawdown risk
        if max_drawdown > self._max_drawdown_threshold:
            risk_score += 3
        elif max_drawdown > self._max_drawdown_threshold * Decimal("0.7"):
            risk_score += 2
        elif max_drawdown > self._max_drawdown_threshold * Decimal("0.5"):
            risk_score += 1
        
        # Sharpe ratio (lower is worse)
        if sharpe_ratio < Decimal("0"):
            risk_score += 3
        elif sharpe_ratio < self._min_sharpe_ratio * Decimal("0.5"):
            risk_score += 2
        elif sharpe_ratio < self._min_sharpe_ratio:
            risk_score += 1
        
        # Determine risk level
        if risk_score >= 8:
            return RiskLevel.CRITICAL
        elif risk_score >= 5:
            return RiskLevel.HIGH
        elif risk_score >= 3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def check_emergency_stop_conditions(self) -> bool:
        """
        Check if emergency stop conditions are met.

        Returns
        -------
        bool
            True if emergency stop should be triggered.
        """
        if self._emergency_stop_triggered:
            return True

        # Check maximum drawdown
        max_drawdown = self.calculate_max_drawdown()
        if max_drawdown > self._max_drawdown_threshold:
            self._trigger_emergency_stop("max_drawdown_exceeded", max_drawdown)
            return True

        # Check VaR
        var_1d = self.calculate_var()
        if var_1d > Decimal("0.10"):  # 10% daily VaR threshold
            self._trigger_emergency_stop("var_exceeded", var_1d)
            return True

        # Check leverage
        exposure = self._calculate_portfolio_exposure()
        balance = self._get_account_balance()
        if balance > 0:
            leverage = exposure / balance
            if leverage > self._max_leverage * Decimal("1.2"):  # 20% buffer
                self._trigger_emergency_stop("leverage_exceeded", leverage)
                return True

        return False

    def _trigger_emergency_stop(self, reason: str, value: Decimal) -> None:
        """Trigger emergency stop."""
        self._emergency_stop_triggered = True

        event = RiskEvent(
            event_type="emergency_stop",
            risk_level=RiskLevel.CRITICAL,
            symbol="PORTFOLIO",
            metric=reason,
            current_value=value,
            threshold=Decimal("0"),  # Varies by metric
            message=f"Emergency stop triggered: {reason} = {value}",
            timestamp=datetime.now(timezone.utc),
        )

        self._risk_events.append(event)

        self._logger.log_risk_event(
            event_type="emergency_stop",
            symbol="PORTFOLIO",
            risk_metric=reason,
            current_value=float(value),
            threshold=0.0,
            action="STOP_ALL_TRADING",
        )

    def reset_emergency_stop(self) -> None:
        """Reset emergency stop flag."""
        self._emergency_stop_triggered = False
        self._logger.logger.info("Emergency stop reset")

    def add_risk_event(self, event: RiskEvent) -> None:
        """Add a risk event to the history."""
        self._risk_events.append(event)

        self._logger.log_risk_event(
            event_type=event.event_type,
            symbol=event.symbol,
            risk_metric=event.metric,
            current_value=float(event.current_value),
            threshold=float(event.threshold),
            action="MONITOR",
        )

    def get_risk_events(
        self,
        symbol: Optional[str] = None,
        risk_level: Optional[RiskLevel] = None,
        limit: Optional[int] = None,
    ) -> List[RiskEvent]:
        """
        Get risk events history.

        Parameters
        ----------
        symbol : str, optional
            Filter by symbol.
        risk_level : RiskLevel, optional
            Filter by risk level.
        limit : int, optional
            Limit number of results.

        Returns
        -------
        List[RiskEvent]
            Risk events.
        """
        events = self._risk_events

        if symbol:
            events = [e for e in events if e.symbol == symbol]

        if risk_level:
            events = [e for e in events if e.risk_level == risk_level]

        if limit:
            events = events[-limit:]

        return events

    def get_risk_summary(self) -> Dict:
        """
        Get risk management summary.

        Returns
        -------
        Dict
            Risk summary.
        """
        portfolio_metrics = self.get_risk_metrics()

        return {
            "timestamp": datetime.now(timezone.utc),
            "emergency_stop_active": self._emergency_stop_triggered,
            "portfolio_exposure": float(portfolio_metrics.exposure),
            "max_portfolio_exposure": float(self._max_portfolio_exposure),
            "leverage": float(portfolio_metrics.leverage),
            "max_leverage": float(self._max_leverage),
            "var_1d": float(portfolio_metrics.var_1d),
            "max_drawdown": float(portfolio_metrics.max_drawdown),
            "sharpe_ratio": float(portfolio_metrics.sharpe_ratio),
            "risk_level": portfolio_metrics.risk_level.value,
            "recent_risk_events": len([e for e in self._risk_events
                                     if e.timestamp > datetime.now(timezone.utc) - timedelta(hours=24)]),
            "total_risk_events": len(self._risk_events),
        }

    def _calculate_portfolio_exposure(self) -> Decimal:
        """Calculate total portfolio exposure."""
        total_exposure = Decimal("0")

        # Get all positions from portfolio
        for position in self._portfolio.positions_open():
            if position.is_open:
                exposure = abs(position.quantity) * position.avg_px_open
                total_exposure += Decimal(str(exposure))

        return total_exposure

    def _get_position_size(self, symbol: str) -> Decimal:
        """Get position size for a symbol."""
        # Find position for the symbol
        for position in self._portfolio.positions():
            if symbol in str(position.instrument_id):
                return Decimal(str(abs(position.quantity)))

        return Decimal("0")

    def _get_position_exposure(self, symbol: str) -> Decimal:
        """Get position exposure for a symbol."""
        # Find position for the symbol
        for position in self._portfolio.positions():
            if symbol in str(position.instrument_id) and position.is_open:
                exposure = abs(position.quantity) * position.avg_px_open
                return Decimal(str(exposure))

        return Decimal("0")

    def _get_account_balance(self) -> Decimal:
        """Get total account balance."""
        total_balance = Decimal("0")

        # Sum balances across all accounts
        for account in self._portfolio.accounts():
            balance = account.balance_total()
            if balance:
                total_balance += Decimal(str(balance.as_decimal()))

        return total_balance

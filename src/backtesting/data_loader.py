"""
Historical data loader for backtesting the funding rate arbitrage system.

This module provides functionality to load and prepare historical market data
and funding rate data for backtesting.
"""

import asyncio
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import pandas as pd
import numpy as np
from nautilus_trader.adapters.binance.common.enums import BinanceAccountType
from nautilus_trader.adapters.binance.futures.http.client import BinanceFuturesHttpClient
from nautilus_trader.adapters.binance.spot.http.client import BinanceSpotHttpClient
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.data import Bar, QuoteTick
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.core.datetime import dt_to_unix_nanos

from src.data.funding_rate import FundingRateData
from src.utils.logging import TradingLogger


class HistoricalDataLoader:
    """
    Loads historical market data and funding rates for backtesting.
    """
    
    def __init__(
        self,
        data_path: str = "./data",
        cache_data: bool = True,
    ) -> None:
        self._data_path = Path(data_path)
        self._cache_data = cache_data
        
        # Ensure data directory exists
        self._data_path.mkdir(parents=True, exist_ok=True)
        
        # Data storage
        self._spot_data: Dict[str, pd.DataFrame] = {}
        self._futures_data: Dict[str, pd.DataFrame] = {}
        self._funding_rate_data: Dict[str, pd.DataFrame] = {}
        
        # Logger
        self._logger = TradingLogger("data_loader")
    
    async def load_historical_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        timeframe: str = "1m",
        include_funding_rates: bool = True,
    ) -> Dict[str, Dict]:
        """
        Load historical data for backtesting.
        
        Parameters
        ----------
        symbols : List[str]
            List of symbols to load data for.
        start_date : datetime
            Start date for data loading.
        end_date : datetime
            End date for data loading.
        timeframe : str, default "1m"
            Timeframe for OHLCV data.
        include_funding_rates : bool, default True
            Whether to load funding rate data.
        
        Returns
        -------
        Dict[str, Dict]
            Dictionary containing loaded data for each symbol.
        """
        data = {}
        
        for symbol in symbols:
            self._logger.logger.info(f"Loading historical data for {symbol}")
            
            symbol_data = {}
            
            # Load spot data
            spot_data = await self._load_spot_data(symbol, start_date, end_date, timeframe)
            if spot_data is not None:
                symbol_data["spot"] = spot_data
            
            # Load futures data
            futures_data = await self._load_futures_data(symbol, start_date, end_date, timeframe)
            if futures_data is not None:
                symbol_data["futures"] = futures_data
            
            # Load funding rate data
            if include_funding_rates:
                funding_data = await self._load_funding_rate_data(symbol, start_date, end_date)
                if funding_data is not None:
                    symbol_data["funding_rates"] = funding_data
            
            if symbol_data:
                data[symbol] = symbol_data
                self._logger.logger.info(f"Loaded data for {symbol}")
            else:
                self._logger.logger.warning(f"No data loaded for {symbol}")
        
        return data
    
    async def _load_spot_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str,
    ) -> Optional[pd.DataFrame]:
        """Load spot market data."""
        cache_file = self._data_path / f"{symbol}_spot_{timeframe}_{start_date.date()}_{end_date.date()}.parquet"
        
        # Try to load from cache
        if self._cache_data and cache_file.exists():
            try:
                df = pd.read_parquet(cache_file)
                self._logger.logger.info(f"Loaded spot data from cache: {cache_file}")
                return df
            except Exception as e:
                self._logger.logger.warning(f"Failed to load cached data: {e}")
        
        # Load from API (simulated - in real implementation would use Binance API)
        df = self._simulate_spot_data(symbol, start_date, end_date, timeframe)
        
        # Cache the data
        if self._cache_data and df is not None:
            try:
                df.to_parquet(cache_file)
                self._logger.logger.info(f"Cached spot data: {cache_file}")
            except Exception as e:
                self._logger.logger.warning(f"Failed to cache data: {e}")
        
        return df
    
    async def _load_futures_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str,
    ) -> Optional[pd.DataFrame]:
        """Load futures market data."""
        cache_file = self._data_path / f"{symbol}_futures_{timeframe}_{start_date.date()}_{end_date.date()}.parquet"
        
        # Try to load from cache
        if self._cache_data and cache_file.exists():
            try:
                df = pd.read_parquet(cache_file)
                self._logger.logger.info(f"Loaded futures data from cache: {cache_file}")
                return df
            except Exception as e:
                self._logger.logger.warning(f"Failed to load cached data: {e}")
        
        # Load from API (simulated - in real implementation would use Binance API)
        df = self._simulate_futures_data(symbol, start_date, end_date, timeframe)
        
        # Cache the data
        if self._cache_data and df is not None:
            try:
                df.to_parquet(cache_file)
                self._logger.logger.info(f"Cached futures data: {cache_file}")
            except Exception as e:
                self._logger.logger.warning(f"Failed to cache data: {e}")
        
        return df
    
    async def _load_funding_rate_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
    ) -> Optional[pd.DataFrame]:
        """Load funding rate data."""
        cache_file = self._data_path / f"{symbol}_funding_{start_date.date()}_{end_date.date()}.parquet"
        
        # Try to load from cache
        if self._cache_data and cache_file.exists():
            try:
                df = pd.read_parquet(cache_file)
                self._logger.logger.info(f"Loaded funding rate data from cache: {cache_file}")
                return df
            except Exception as e:
                self._logger.logger.warning(f"Failed to load cached data: {e}")
        
        # Load from API (simulated - in real implementation would use Binance API)
        df = self._simulate_funding_rate_data(symbol, start_date, end_date)
        
        # Cache the data
        if self._cache_data and df is not None:
            try:
                df.to_parquet(cache_file)
                self._logger.logger.info(f"Cached funding rate data: {cache_file}")
            except Exception as e:
                self._logger.logger.warning(f"Failed to cache data: {e}")
        
        return df
    
    def _simulate_spot_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str,
    ) -> pd.DataFrame:
        """Simulate spot market data for backtesting."""
        # Create time index
        freq_map = {"1m": "1T", "5m": "5T", "15m": "15T", "1h": "1H", "1d": "1D"}
        freq = freq_map.get(timeframe, "1T")
        
        timestamps = pd.date_range(start_date, end_date, freq=freq)
        
        # Simulate price data (random walk with trend)
        np.random.seed(42)  # For reproducible results
        base_price = 47000.0  # Starting price for BTC
        
        # Generate price series
        returns = np.random.normal(0, 0.001, len(timestamps))  # 0.1% volatility
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Create OHLCV data
        df = pd.DataFrame({
            "timestamp": timestamps,
            "open": prices,
            "high": [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
            "low": [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
            "close": prices,
            "volume": np.random.uniform(100, 1000, len(timestamps)),
        })
        
        df.set_index("timestamp", inplace=True)
        return df
    
    def _simulate_futures_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        timeframe: str,
    ) -> pd.DataFrame:
        """Simulate futures market data for backtesting."""
        # Get spot data first
        spot_df = self._simulate_spot_data(symbol, start_date, end_date, timeframe)
        
        # Futures prices are typically slightly higher than spot (contango)
        # Add small random spread
        np.random.seed(43)  # Different seed for futures
        spread = np.random.normal(10, 5, len(spot_df))  # Average $10 premium
        
        futures_df = spot_df.copy()
        futures_df["open"] = spot_df["open"] + spread
        futures_df["high"] = spot_df["high"] + spread
        futures_df["low"] = spot_df["low"] + spread
        futures_df["close"] = spot_df["close"] + spread
        
        return futures_df
    
    def _simulate_funding_rate_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
    ) -> pd.DataFrame:
        """Simulate funding rate data for backtesting."""
        # Funding occurs every 8 hours
        funding_times = pd.date_range(
            start_date.replace(hour=0, minute=0, second=0),
            end_date,
            freq="8H"
        )
        
        # Simulate funding rates (typically between -0.1% and 0.1%)
        np.random.seed(44)  # Different seed for funding rates
        funding_rates = np.random.normal(0.0001, 0.0005, len(funding_times))  # 0.01% mean, 0.05% std
        
        df = pd.DataFrame({
            "timestamp": funding_times,
            "funding_rate": funding_rates,
            "funding_time": [int(t.timestamp() * 1000) for t in funding_times],
        })
        
        df.set_index("timestamp", inplace=True)
        return df
    
    def get_data_summary(self) -> Dict:
        """Get summary of loaded data."""
        summary = {
            "spot_symbols": list(self._spot_data.keys()),
            "futures_symbols": list(self._futures_data.keys()),
            "funding_rate_symbols": list(self._funding_rate_data.keys()),
            "data_path": str(self._data_path),
            "cache_enabled": self._cache_data,
        }
        
        # Add data ranges for each symbol
        for symbol in self._spot_data:
            if symbol in self._spot_data:
                df = self._spot_data[symbol]
                summary[f"{symbol}_spot_range"] = f"{df.index.min()} to {df.index.max()}"
                summary[f"{symbol}_spot_records"] = len(df)
        
        return summary

"""
Backtesting framework for the funding rate arbitrage system.

This module provides comprehensive backtesting infrastructure using historical
Binance data to validate the arbitrage strategy performance.
"""

from src.backtesting.engine import BacktestEngine
from src.backtesting.data_loader import HistoricalDataLoader
from src.backtesting.results import BacktestResults, BacktestMetrics

__all__ = [
    "BacktestEngine",
    "HistoricalDataLoader", 
    "BacktestResults",
    "BacktestMetrics",
]

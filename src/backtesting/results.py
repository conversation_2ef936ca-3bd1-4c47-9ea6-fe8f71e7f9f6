"""
Backtesting results and metrics for the funding rate arbitrage system.

This module provides comprehensive analysis and reporting of backtest results.
"""

from decimal import Decimal
from datetime import datetime, timezone
from typing import Dict, List, Optional, NamedTuple
from dataclasses import dataclass

import pandas as pd
import numpy as np
from nautilus_trader.backtest.results import BacktestResult

from src.utils.calculations import calculate_sharpe_ratio, calculate_max_drawdown
from src.utils.logging import TradingLogger


@dataclass
class TradeResult:
    """Individual trade result."""
    trade_id: str
    symbol: str
    entry_time: datetime
    exit_time: datetime
    arbitrage_type: str  # "long" or "short"
    entry_funding_rate: Decimal
    position_size: Decimal
    entry_spot_price: Decimal
    entry_futures_price: Decimal
    exit_spot_price: Decimal
    exit_futures_price: Decimal
    trading_pnl: Decimal
    funding_pnl: Decimal
    total_pnl: Decimal
    holding_period_hours: float
    return_percentage: Decimal
    fees_paid: Decimal


@dataclass
class BacktestMetrics:
    """Comprehensive backtest performance metrics."""
    
    # Basic metrics
    start_date: datetime
    end_date: datetime
    duration_days: int
    total_trades: int
    winning_trades: int
    losing_trades: int
    
    # Performance metrics
    total_return: Decimal
    total_return_percentage: Decimal
    annualized_return: Decimal
    volatility: Decimal
    sharpe_ratio: Decimal
    max_drawdown: Decimal
    max_drawdown_duration_days: int
    
    # Trading metrics
    win_rate: Decimal
    profit_factor: Decimal
    average_win: Decimal
    average_loss: Decimal
    largest_win: Decimal
    largest_loss: Decimal
    
    # Funding rate specific metrics
    total_funding_pnl: Decimal
    total_trading_pnl: Decimal
    funding_pnl_percentage: Decimal
    average_funding_rate: Decimal
    funding_rate_volatility: Decimal
    
    # Risk metrics
    var_95: Decimal  # 95% Value at Risk
    cvar_95: Decimal  # 95% Conditional Value at Risk
    calmar_ratio: Decimal  # Annual return / Max drawdown
    sortino_ratio: Decimal
    
    # Efficiency metrics
    trades_per_day: Decimal
    average_holding_period_hours: Decimal
    turnover_rate: Decimal


class BacktestResults:
    """
    Comprehensive backtest results analysis.
    """
    
    def __init__(
        self,
        start_capital: Decimal,
        start_date: datetime,
        end_date: datetime,
    ) -> None:
        self._start_capital = start_capital
        self._start_date = start_date
        self._end_date = end_date
        
        # Results storage
        self._trades: List[TradeResult] = []
        self._equity_curve: List[tuple[datetime, Decimal]] = []
        self._daily_returns: List[tuple[datetime, Decimal]] = []
        self._funding_payments: List[tuple[datetime, str, Decimal]] = []
        
        # Cached metrics
        self._metrics: Optional[BacktestMetrics] = None
        self._metrics_dirty = True
        
        # Logger
        self._logger = TradingLogger("backtest_results")
    
    def add_trade(self, trade: TradeResult) -> None:
        """Add a completed trade to results."""
        self._trades.append(trade)
        self._metrics_dirty = True
        
        self._logger.logger.info(
            f"Trade completed: {trade.trade_id}",
            symbol=trade.symbol,
            pnl=float(trade.total_pnl),
            return_pct=float(trade.return_percentage),
        )
    
    def add_equity_point(self, timestamp: datetime, equity: Decimal) -> None:
        """Add equity curve point."""
        self._equity_curve.append((timestamp, equity))
        
        # Calculate daily return if we have previous data
        if len(self._equity_curve) > 1:
            prev_equity = self._equity_curve[-2][1]
            if prev_equity > 0:
                daily_return = (equity - prev_equity) / prev_equity
                self._daily_returns.append((timestamp, daily_return))
        
        self._metrics_dirty = True
    
    def add_funding_payment(
        self,
        timestamp: datetime,
        symbol: str,
        amount: Decimal,
    ) -> None:
        """Add funding payment record."""
        self._funding_payments.append((timestamp, symbol, amount))
        self._metrics_dirty = True
    
    def calculate_metrics(self) -> BacktestMetrics:
        """Calculate comprehensive backtest metrics."""
        if not self._metrics_dirty and self._metrics:
            return self._metrics
        
        # Basic metrics
        duration_days = (self._end_date - self._start_date).days
        total_trades = len(self._trades)
        winning_trades = len([t for t in self._trades if t.total_pnl > 0])
        losing_trades = len([t for t in self._trades if t.total_pnl < 0])
        
        # Performance metrics
        final_equity = self._equity_curve[-1][1] if self._equity_curve else self._start_capital
        total_return = final_equity - self._start_capital
        total_return_percentage = (total_return / self._start_capital) * 100
        
        # Annualized return
        years = duration_days / 365.25
        annualized_return = ((final_equity / self._start_capital) ** (1 / years) - 1) * 100 if years > 0 else Decimal("0")
        
        # Volatility and Sharpe ratio
        returns_series = pd.Series([float(r) for _, r in self._daily_returns])
        volatility = Decimal(str(returns_series.std() * np.sqrt(252))) if len(returns_series) > 1 else Decimal("0")
        sharpe_ratio = calculate_sharpe_ratio(returns_series) if len(returns_series) > 1 else Decimal("0")
        
        # Max drawdown
        equity_series = pd.Series([float(e) for _, e in self._equity_curve])
        max_drawdown, _, _ = calculate_max_drawdown(equity_series) if len(equity_series) > 1 else (Decimal("0"), None, None)
        
        # Trading metrics
        win_rate = Decimal(str(winning_trades / total_trades)) if total_trades > 0 else Decimal("0")
        
        wins = [t.total_pnl for t in self._trades if t.total_pnl > 0]
        losses = [abs(t.total_pnl) for t in self._trades if t.total_pnl < 0]
        
        average_win = Decimal(str(np.mean([float(w) for w in wins]))) if wins else Decimal("0")
        average_loss = Decimal(str(np.mean([float(l) for l in losses]))) if losses else Decimal("0")
        largest_win = max(wins) if wins else Decimal("0")
        largest_loss = max(losses) if losses else Decimal("0")
        
        profit_factor = (average_win * len(wins)) / (average_loss * len(losses)) if losses else Decimal("999")
        
        # Funding rate specific metrics
        total_funding_pnl = sum(t.funding_pnl for t in self._trades)
        total_trading_pnl = sum(t.trading_pnl for t in self._trades)
        funding_pnl_percentage = (total_funding_pnl / total_return * 100) if total_return != 0 else Decimal("0")
        
        funding_rates = [t.entry_funding_rate for t in self._trades]
        average_funding_rate = Decimal(str(np.mean([float(fr) for fr in funding_rates]))) if funding_rates else Decimal("0")
        funding_rate_volatility = Decimal(str(np.std([float(fr) for fr in funding_rates]))) if len(funding_rates) > 1 else Decimal("0")
        
        # Risk metrics
        var_95 = Decimal(str(returns_series.quantile(0.05))) if len(returns_series) > 1 else Decimal("0")
        cvar_95 = Decimal(str(returns_series[returns_series <= returns_series.quantile(0.05)].mean())) if len(returns_series) > 1 else Decimal("0")
        calmar_ratio = annualized_return / (max_drawdown * 100) if max_drawdown > 0 else Decimal("0")
        
        # Sortino ratio (downside deviation)
        downside_returns = returns_series[returns_series < 0]
        downside_std = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 1 else 0
        sortino_ratio = Decimal(str((float(annualized_return) / 100) / downside_std)) if downside_std > 0 else Decimal("0")
        
        # Efficiency metrics
        trades_per_day = Decimal(str(total_trades / duration_days)) if duration_days > 0 else Decimal("0")
        average_holding_period = np.mean([t.holding_period_hours for t in self._trades]) if self._trades else 0
        average_holding_period_hours = Decimal(str(average_holding_period))
        
        # Turnover rate (simplified)
        turnover_rate = Decimal("0")  # Would need position size data to calculate properly
        
        self._metrics = BacktestMetrics(
            start_date=self._start_date,
            end_date=self._end_date,
            duration_days=duration_days,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            total_return=total_return,
            total_return_percentage=total_return_percentage,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            max_drawdown_duration_days=0,  # Would need to calculate from drawdown periods
            win_rate=win_rate,
            profit_factor=profit_factor,
            average_win=average_win,
            average_loss=average_loss,
            largest_win=largest_win,
            largest_loss=largest_loss,
            total_funding_pnl=total_funding_pnl,
            total_trading_pnl=total_trading_pnl,
            funding_pnl_percentage=funding_pnl_percentage,
            average_funding_rate=average_funding_rate,
            funding_rate_volatility=funding_rate_volatility,
            var_95=var_95,
            cvar_95=cvar_95,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio,
            trades_per_day=trades_per_day,
            average_holding_period_hours=average_holding_period_hours,
            turnover_rate=turnover_rate,
        )
        
        self._metrics_dirty = False
        return self._metrics
    
    def get_trades_dataframe(self) -> pd.DataFrame:
        """Get trades as pandas DataFrame."""
        if not self._trades:
            return pd.DataFrame()
        
        data = []
        for trade in self._trades:
            data.append({
                "trade_id": trade.trade_id,
                "symbol": trade.symbol,
                "entry_time": trade.entry_time,
                "exit_time": trade.exit_time,
                "arbitrage_type": trade.arbitrage_type,
                "entry_funding_rate": float(trade.entry_funding_rate),
                "position_size": float(trade.position_size),
                "entry_spot_price": float(trade.entry_spot_price),
                "entry_futures_price": float(trade.entry_futures_price),
                "exit_spot_price": float(trade.exit_spot_price),
                "exit_futures_price": float(trade.exit_futures_price),
                "trading_pnl": float(trade.trading_pnl),
                "funding_pnl": float(trade.funding_pnl),
                "total_pnl": float(trade.total_pnl),
                "holding_period_hours": trade.holding_period_hours,
                "return_percentage": float(trade.return_percentage),
                "fees_paid": float(trade.fees_paid),
            })
        
        return pd.DataFrame(data)
    
    def get_equity_curve_dataframe(self) -> pd.DataFrame:
        """Get equity curve as pandas DataFrame."""
        if not self._equity_curve:
            return pd.DataFrame()
        
        return pd.DataFrame(self._equity_curve, columns=["timestamp", "equity"])
    
    def get_summary_report(self) -> str:
        """Generate a summary report of backtest results."""
        metrics = self.calculate_metrics()
        
        report = f"""
Funding Rate Arbitrage Backtest Results
======================================

Period: {metrics.start_date.date()} to {metrics.end_date.date()} ({metrics.duration_days} days)

Performance Summary:
- Total Return: ${metrics.total_return:,.2f} ({metrics.total_return_percentage:.2f}%)
- Annualized Return: {metrics.annualized_return:.2f}%
- Volatility: {metrics.volatility:.2f}%
- Sharpe Ratio: {metrics.sharpe_ratio:.2f}
- Max Drawdown: {metrics.max_drawdown:.2f}%
- Calmar Ratio: {metrics.calmar_ratio:.2f}

Trading Statistics:
- Total Trades: {metrics.total_trades}
- Winning Trades: {metrics.winning_trades}
- Losing Trades: {metrics.losing_trades}
- Win Rate: {metrics.win_rate:.2f}%
- Profit Factor: {metrics.profit_factor:.2f}
- Average Win: ${metrics.average_win:,.2f}
- Average Loss: ${metrics.average_loss:,.2f}
- Largest Win: ${metrics.largest_win:,.2f}
- Largest Loss: ${metrics.largest_loss:,.2f}

Funding Rate Analysis:
- Total Funding PnL: ${metrics.total_funding_pnl:,.2f}
- Total Trading PnL: ${metrics.total_trading_pnl:,.2f}
- Funding PnL %: {metrics.funding_pnl_percentage:.2f}%
- Average Funding Rate: {metrics.average_funding_rate:.4f}%
- Funding Rate Volatility: {metrics.funding_rate_volatility:.4f}%

Risk Metrics:
- VaR (95%): {metrics.var_95:.4f}
- CVaR (95%): {metrics.cvar_95:.4f}
- Sortino Ratio: {metrics.sortino_ratio:.2f}

Efficiency:
- Trades per Day: {metrics.trades_per_day:.2f}
- Average Holding Period: {metrics.average_holding_period_hours:.1f} hours
"""
        
        return report

"""
Backtesting engine for the funding rate arbitrage system.

This module provides the main backtesting engine that simulates the trading
strategy using historical data.
"""

import asyncio
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Callable
from uuid import uuid4

import pandas as pd
from nautilus_trader.backtest.engine import BacktestEngine as NautilusBacktestEngine
from nautilus_trader.backtest.node import BacktestNode
from nautilus_trader.config import BacktestEngineConfig
from nautilus_trader.model.identifiers import InstrumentId

from src.backtesting.data_loader import HistoricalDataLoader
from src.backtesting.results import BacktestResults, TradeResult, BacktestMetrics
from src.strategies.analysis_engine import FundingRateAnalysisEngine, ArbitrageSignal, SignalType
from src.data.funding_rate import FundingRateData
from src.utils.calculations import calculate_funding_rate_profit
from src.utils.time_utils import get_funding_times_in_range
from src.utils.logging import TradingLogger


class BacktestEngine:
    """
    Backtesting engine for funding rate arbitrage strategies.
    """
    
    def __init__(
        self,
        start_capital: Decimal = Decimal("10000.0"),
        commission_rate: Decimal = Decimal("0.001"),  # 0.1%
        slippage_bps: int = 1,  # 1 basis point
    ) -> None:
        self._start_capital = start_capital
        self._commission_rate = commission_rate
        self._slippage_bps = slippage_bps
        
        # Components
        self._data_loader = HistoricalDataLoader()
        self._results: Optional[BacktestResults] = None
        
        # Simulation state
        self._current_time: Optional[datetime] = None
        self._current_capital = start_capital
        self._positions: Dict[str, Dict] = {}  # symbol -> position info
        self._trade_id_counter = 0
        
        # Data storage
        self._historical_data: Dict[str, Dict] = {}
        
        # Callbacks
        self._trade_callbacks: List[Callable] = []
        
        # Logger
        self._logger = TradingLogger("backtest_engine")
    
    def add_trade_callback(self, callback: Callable) -> None:
        """Add callback for trade events."""
        self._trade_callbacks.append(callback)
    
    async def run_backtest(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        strategy_config: Dict,
    ) -> BacktestResults:
        """
        Run a complete backtest.
        
        Parameters
        ----------
        symbols : List[str]
            Symbols to trade.
        start_date : datetime
            Backtest start date.
        end_date : datetime
            Backtest end date.
        strategy_config : Dict
            Strategy configuration parameters.
        
        Returns
        -------
        BacktestResults
            Backtest results and metrics.
        """
        self._logger.logger.info(
            f"Starting backtest: {start_date.date()} to {end_date.date()}",
            symbols=symbols,
            start_capital=float(self._start_capital),
        )
        
        # Initialize results
        self._results = BacktestResults(
            start_capital=self._start_capital,
            start_date=start_date,
            end_date=end_date,
        )
        
        # Load historical data
        self._historical_data = await self._data_loader.load_historical_data(
            symbols=symbols,
            start_date=start_date,
            end_date=end_date,
            timeframe="1m",
            include_funding_rates=True,
        )
        
        # Run simulation
        await self._run_simulation(symbols, start_date, end_date, strategy_config)
        
        # Calculate final metrics
        metrics = self._results.calculate_metrics()
        
        self._logger.logger.info(
            "Backtest completed",
            total_trades=metrics.total_trades,
            total_return=float(metrics.total_return),
            win_rate=float(metrics.win_rate),
            sharpe_ratio=float(metrics.sharpe_ratio),
        )
        
        return self._results
    
    async def _run_simulation(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        strategy_config: Dict,
    ) -> None:
        """Run the main simulation loop."""
        # Create time index for simulation (1-minute intervals)
        time_index = pd.date_range(start_date, end_date, freq="1T")
        
        # Initialize positions
        for symbol in symbols:
            self._positions[symbol] = {
                "status": "closed",
                "arbitrage_type": None,
                "entry_time": None,
                "entry_funding_rate": None,
                "spot_size": Decimal("0"),
                "futures_size": Decimal("0"),
                "entry_spot_price": None,
                "entry_futures_price": None,
            }
        
        # Simulation loop
        for timestamp in time_index:
            self._current_time = timestamp
            
            # Update equity curve
            self._results.add_equity_point(timestamp, self._current_capital)
            
            # Process each symbol
            for symbol in symbols:
                await self._process_symbol(symbol, timestamp, strategy_config)
            
            # Check for funding payments (every 8 hours)
            if timestamp.hour in [0, 8, 16] and timestamp.minute == 0:
                await self._process_funding_payments(symbols, timestamp)
    
    async def _process_symbol(
        self,
        symbol: str,
        timestamp: datetime,
        strategy_config: Dict,
    ) -> None:
        """Process a single symbol at a given timestamp."""
        if symbol not in self._historical_data:
            return
        
        symbol_data = self._historical_data[symbol]
        
        # Get current market data
        spot_data = symbol_data.get("spot")
        futures_data = symbol_data.get("futures")
        funding_data = symbol_data.get("funding_rates")
        
        if not all([spot_data is not None, futures_data is not None, funding_data is not None]):
            return
        
        # Get prices at current timestamp
        try:
            spot_price = self._get_price_at_time(spot_data, timestamp)
            futures_price = self._get_price_at_time(futures_data, timestamp)
            funding_rate = self._get_funding_rate_at_time(funding_data, timestamp)
        except (KeyError, IndexError):
            return  # No data available at this timestamp
        
        if not all([spot_price, futures_price, funding_rate is not None]):
            return
        
        # Analyze arbitrage opportunity
        signal = self._analyze_arbitrage_opportunity(
            symbol, spot_price, futures_price, funding_rate, strategy_config
        )
        
        # Execute trades based on signal
        if signal:
            await self._execute_signal(symbol, signal, spot_price, futures_price)
    
    def _get_price_at_time(self, data: pd.DataFrame, timestamp: datetime) -> Optional[Decimal]:
        """Get price at a specific timestamp."""
        try:
            # Find the closest timestamp
            closest_idx = data.index.get_indexer([timestamp], method="nearest")[0]
            if closest_idx >= 0:
                price = data.iloc[closest_idx]["close"]
                return Decimal(str(price))
        except (KeyError, IndexError):
            pass
        return None
    
    def _get_funding_rate_at_time(self, data: pd.DataFrame, timestamp: datetime) -> Optional[Decimal]:
        """Get funding rate at a specific timestamp."""
        try:
            # Find the most recent funding rate
            recent_data = data[data.index <= timestamp]
            if not recent_data.empty:
                funding_rate = recent_data.iloc[-1]["funding_rate"]
                return Decimal(str(funding_rate))
        except (KeyError, IndexError):
            pass
        return None
    
    def _analyze_arbitrage_opportunity(
        self,
        symbol: str,
        spot_price: Decimal,
        futures_price: Decimal,
        funding_rate: Decimal,
        strategy_config: Dict,
    ) -> Optional[ArbitrageSignal]:
        """Analyze arbitrage opportunity."""
        # Get configuration parameters
        min_funding_rate_threshold = Decimal(str(strategy_config.get("min_funding_rate_threshold", 0.01)))
        min_arbitrage_profit = Decimal(str(strategy_config.get("min_arbitrage_profit", 0.005)))
        max_spread_threshold = Decimal(str(strategy_config.get("max_spread_threshold", 0.02)))
        
        # Calculate spread
        spread = futures_price - spot_price
        spread_percentage = (spread / spot_price) * 100
        
        # Check spread threshold
        if abs(spread_percentage) > max_spread_threshold:
            return None
        
        # Determine signal type
        signal_type = SignalType.NO_SIGNAL
        expected_profit = Decimal("0")
        confidence = Decimal("0")
        
        if funding_rate > min_funding_rate_threshold:
            # Positive funding rate: long arbitrage
            signal_type = SignalType.LONG_ARBITRAGE
            expected_profit = funding_rate
            confidence = min(Decimal("1.0"), funding_rate / min_funding_rate_threshold)
        elif funding_rate < -min_funding_rate_threshold:
            # Negative funding rate: short arbitrage
            signal_type = SignalType.SHORT_ARBITRAGE
            expected_profit = abs(funding_rate)
            confidence = min(Decimal("1.0"), abs(funding_rate) / min_funding_rate_threshold)
        
        if expected_profit < min_arbitrage_profit:
            return None
        
        # Create signal
        return ArbitrageSignal(
            symbol=symbol,
            signal_type=signal_type,
            funding_rate=funding_rate,
            spot_price=spot_price,
            futures_price=futures_price,
            spread=spread_percentage,
            expected_profit=expected_profit,
            confidence=confidence,
            timestamp=self._current_time,
            next_funding_time=self._current_time + timedelta(hours=8),  # Simplified
            time_to_funding=timedelta(hours=8),  # Simplified
        )
    
    async def _execute_signal(
        self,
        symbol: str,
        signal: ArbitrageSignal,
        spot_price: Decimal,
        futures_price: Decimal,
    ) -> None:
        """Execute trading signal."""
        position = self._positions[symbol]
        
        if signal.signal_type == SignalType.NO_SIGNAL:
            return
        
        # Check if we should close existing position
        if position["status"] == "open":
            # Simple exit condition: hold for 8 hours or opposite signal
            holding_time = self._current_time - position["entry_time"]
            if (holding_time.total_seconds() > 8 * 3600 or  # 8 hours
                (position["arbitrage_type"] == "long" and signal.signal_type == SignalType.SHORT_ARBITRAGE) or
                (position["arbitrage_type"] == "short" and signal.signal_type == SignalType.LONG_ARBITRAGE)):
                
                await self._close_position(symbol, spot_price, futures_price)
        
        # Open new position if no position is open
        if position["status"] == "closed":
            await self._open_position(symbol, signal, spot_price, futures_price)
    
    async def _open_position(
        self,
        symbol: str,
        signal: ArbitrageSignal,
        spot_price: Decimal,
        futures_price: Decimal,
    ) -> None:
        """Open a new arbitrage position."""
        # Calculate position size (simplified: 10% of capital)
        position_value = self._current_capital * Decimal("0.1")
        position_size = position_value / futures_price
        
        # Apply slippage and fees
        slippage = Decimal(str(self._slippage_bps)) / Decimal("10000")
        fees = position_value * self._commission_rate * 2  # Both legs
        
        # Update capital for fees
        self._current_capital -= fees
        
        # Update position
        arbitrage_type = "long" if signal.signal_type == SignalType.LONG_ARBITRAGE else "short"
        
        self._positions[symbol].update({
            "status": "open",
            "arbitrage_type": arbitrage_type,
            "entry_time": self._current_time,
            "entry_funding_rate": signal.funding_rate,
            "spot_size": position_size if arbitrage_type == "long" else -position_size,
            "futures_size": -position_size if arbitrage_type == "long" else position_size,
            "entry_spot_price": spot_price,
            "entry_futures_price": futures_price,
        })
        
        self._logger.logger.info(
            f"Opened {arbitrage_type} arbitrage position",
            symbol=symbol,
            size=float(position_size),
            funding_rate=float(signal.funding_rate),
        )
    
    async def _close_position(
        self,
        symbol: str,
        spot_price: Decimal,
        futures_price: Decimal,
    ) -> None:
        """Close an arbitrage position."""
        position = self._positions[symbol]
        
        if position["status"] != "open":
            return
        
        # Calculate PnL
        spot_pnl = (spot_price - position["entry_spot_price"]) * position["spot_size"]
        futures_pnl = (futures_price - position["entry_futures_price"]) * position["futures_size"]
        trading_pnl = spot_pnl + futures_pnl
        
        # Calculate funding PnL (simplified)
        holding_hours = (self._current_time - position["entry_time"]).total_seconds() / 3600
        funding_periods = max(1, int(holding_hours / 8))  # At least 1 period
        
        notional_value = abs(position["futures_size"]) * position["entry_futures_price"]
        funding_pnl = notional_value * position["entry_funding_rate"] * Decimal(str(funding_periods))
        
        # Adjust funding PnL based on position type
        if position["arbitrage_type"] == "short":
            funding_pnl = -funding_pnl  # Short arbitrage receives negative funding
        
        total_pnl = trading_pnl + funding_pnl
        
        # Apply fees for closing
        fees = notional_value * self._commission_rate * 2
        total_pnl -= fees
        
        # Update capital
        self._current_capital += total_pnl
        
        # Create trade result
        self._trade_id_counter += 1
        trade = TradeResult(
            trade_id=f"trade_{self._trade_id_counter}",
            symbol=symbol,
            entry_time=position["entry_time"],
            exit_time=self._current_time,
            arbitrage_type=position["arbitrage_type"],
            entry_funding_rate=position["entry_funding_rate"],
            position_size=abs(position["spot_size"]),
            entry_spot_price=position["entry_spot_price"],
            entry_futures_price=position["entry_futures_price"],
            exit_spot_price=spot_price,
            exit_futures_price=futures_price,
            trading_pnl=trading_pnl,
            funding_pnl=funding_pnl,
            total_pnl=total_pnl,
            holding_period_hours=holding_hours,
            return_percentage=(total_pnl / notional_value) * 100,
            fees_paid=fees,
        )
        
        # Add to results
        self._results.add_trade(trade)
        
        # Notify callbacks
        for callback in self._trade_callbacks:
            callback(trade)
        
        # Reset position
        self._positions[symbol].update({
            "status": "closed",
            "arbitrage_type": None,
            "entry_time": None,
            "entry_funding_rate": None,
            "spot_size": Decimal("0"),
            "futures_size": Decimal("0"),
            "entry_spot_price": None,
            "entry_futures_price": None,
        })
        
        self._logger.logger.info(
            f"Closed arbitrage position",
            symbol=symbol,
            pnl=float(total_pnl),
            holding_hours=holding_hours,
        )
    
    async def _process_funding_payments(self, symbols: List[str], timestamp: datetime) -> None:
        """Process funding payments for open positions."""
        for symbol in symbols:
            position = self._positions[symbol]
            
            if position["status"] == "open":
                # Calculate funding payment
                notional_value = abs(position["futures_size"]) * position["entry_futures_price"]
                funding_payment = notional_value * position["entry_funding_rate"]
                
                # Adjust based on position type
                if position["arbitrage_type"] == "short":
                    funding_payment = -funding_payment
                
                # Record funding payment
                self._results.add_funding_payment(timestamp, symbol, funding_payment)

"""
Command line interface for the funding rate arbitrage system.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.table import Table

from src.config import Config


console = Console()


@click.group()
@click.option("--config", "-c", type=click.Path(exists=True), help="Configuration file path")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose logging")
@click.pass_context
def main(ctx: click.Context, config: Optional[str], verbose: bool) -> None:
    """Binance Funding Rate Arbitrage System CLI."""
    ctx.ensure_object(dict)
    
    # Load configuration
    if config:
        ctx.obj["config"] = Config.from_yaml(Path(config))
    else:
        ctx.obj["config"] = Config()
    
    if verbose:
        ctx.obj["config"].logging.level = "DEBUG"


@main.command()
@click.option("--days", "-d", default=30, help="Number of days of historical data")
@click.option("--instruments", "-i", multiple=True, help="Instruments to backtest")
@click.pass_context
def backtest(ctx: click.Context, days: int, instruments: tuple) -> None:
    """Run backtesting on historical data."""
    config = ctx.obj["config"]
    
    console.print("[bold blue]Starting Backtest[/bold blue]")
    console.print(f"Days: {days}")
    console.print(f"Instruments: {instruments or config.strategy.instruments}")
    
    # TODO: Implement backtest runner
    console.print("[yellow]Backtest functionality not yet implemented[/yellow]")


@main.command()
@click.option("--duration", "-d", default=3600, help="Duration in seconds")
@click.pass_context
def paper_trade(ctx: click.Context, duration: int) -> None:
    """Run paper trading with live data."""
    config = ctx.obj["config"]
    
    console.print("[bold green]Starting Paper Trading[/bold green]")
    console.print(f"Duration: {duration} seconds")
    console.print(f"Instruments: {config.strategy.instruments}")
    
    # TODO: Implement paper trading
    console.print("[yellow]Paper trading functionality not yet implemented[/yellow]")


@main.command()
@click.confirmation_option(prompt="Are you sure you want to start live trading?")
@click.pass_context
def live_trade(ctx: click.Context) -> None:
    """Start live trading (REAL MONEY)."""
    config = ctx.obj["config"]
    
    if config.binance.testnet:
        console.print("[yellow]Warning: Running on testnet[/yellow]")
    else:
        console.print("[bold red]WARNING: LIVE TRADING WITH REAL MONEY[/bold red]")
    
    console.print("[bold green]Starting Live Trading[/bold green]")
    console.print(f"Instruments: {config.strategy.instruments}")
    
    # TODO: Implement live trading
    console.print("[yellow]Live trading functionality not yet implemented[/yellow]")


@main.command()
@click.pass_context
def status(ctx: click.Context) -> None:
    """Show system status and configuration."""
    config = ctx.obj["config"]
    
    # Create status table
    table = Table(title="Funding Rate Arbitrage System Status")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Environment", config.environment)
    table.add_row("Trader ID", config.trader_id)
    table.add_row("Instance ID", config.instance_id)
    table.add_row("Testnet", str(config.binance.testnet))
    table.add_row("Max Position Size", str(config.risk.max_position_size))
    table.add_row("Min Funding Rate", str(config.risk.min_funding_rate_threshold))
    table.add_row("Instruments", ", ".join(config.strategy.instruments))
    
    console.print(table)


@main.command()
@click.option("--output", "-o", type=click.Path(), help="Output file path")
@click.pass_context
def export_config(ctx: click.Context, output: Optional[str]) -> None:
    """Export current configuration to YAML file."""
    config = ctx.obj["config"]
    
    if output:
        output_path = Path(output)
    else:
        output_path = Path("config/config_export.yaml")
    
    output_path.parent.mkdir(parents=True, exist_ok=True)
    config.to_yaml(output_path)
    
    console.print(f"[green]Configuration exported to {output_path}[/green]")


@main.command()
@click.pass_context
def validate_config(ctx: click.Context) -> None:
    """Validate configuration settings."""
    config = ctx.obj["config"]
    
    console.print("[bold blue]Validating Configuration[/bold blue]")
    
    # Basic validation
    errors = []
    warnings = []
    
    # Check API credentials
    if not config.binance.api_key or not config.binance.api_secret:
        errors.append("Binance API credentials not configured")
    
    # Check risk settings
    if config.risk.max_position_size <= 0:
        errors.append("Max position size must be positive")
    
    if config.risk.min_funding_rate_threshold <= 0:
        warnings.append("Very low funding rate threshold may cause excessive trading")
    
    # Check strategy settings
    if not config.strategy.instruments:
        errors.append("No instruments configured for trading")
    
    # Display results
    if errors:
        console.print("[bold red]Configuration Errors:[/bold red]")
        for error in errors:
            console.print(f"  ❌ {error}")
    
    if warnings:
        console.print("[bold yellow]Configuration Warnings:[/bold yellow]")
        for warning in warnings:
            console.print(f"  ⚠️  {warning}")
    
    if not errors and not warnings:
        console.print("[bold green]✅ Configuration is valid[/bold green]")
    
    if errors:
        sys.exit(1)


if __name__ == "__main__":
    main()

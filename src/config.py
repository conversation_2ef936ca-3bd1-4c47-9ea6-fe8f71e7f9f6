"""
Configuration management for the funding rate arbitrage system.
"""

from decimal import Decimal
from pathlib import Path
from typing import Dict, List, Optional

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class BinanceConfig(BaseModel):
    """Binance API configuration."""
    
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    testnet: bool = True
    base_url_http: Optional[str] = None
    base_url_ws: Optional[str] = None
    us: bool = False


class RiskConfig(BaseModel):
    """Risk management configuration."""
    
    max_position_size: Decimal = Field(default=Decimal("1000.0"))
    max_portfolio_exposure: Decimal = Field(default=Decimal("10000.0"))
    min_funding_rate_threshold: Decimal = Field(default=Decimal("0.01"))  # 1%
    max_leverage: Decimal = Field(default=Decimal("3.0"))
    stop_loss_percentage: Decimal = Field(default=Decimal("0.05"))  # 5%
    position_size_percentage: Decimal = Field(default=Decimal("0.1"))  # 10% of portfolio


class StrategyConfig(BaseModel):
    """Trading strategy configuration."""
    
    funding_rate_check_interval: int = Field(default=300)  # 5 minutes
    min_arbitrage_profit: Decimal = Field(default=Decimal("0.005"))  # 0.5%
    max_holding_period: int = Field(default=28800)  # 8 hours in seconds
    rebalance_threshold: Decimal = Field(default=Decimal("0.02"))  # 2%
    instruments: List[str] = Field(default=["BTCUSDT", "ETHUSDT", "ADAUSDT"])


class DataConfig(BaseModel):
    """Data configuration."""
    
    catalog_path: str = Field(default="./data/catalog")
    historical_data_days: int = Field(default=30)
    bar_types: List[str] = Field(default=["1-MINUTE-LAST", "5-MINUTE-LAST"])
    save_raw_data: bool = Field(default=True)


class LoggingConfig(BaseModel):
    """Logging configuration."""
    
    level: str = Field(default="INFO")
    format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    file_path: str = Field(default="./logs/funding_arbitrage.log")
    max_file_size: str = Field(default="10MB")
    backup_count: int = Field(default=5)


class Config(BaseSettings):
    """Main configuration class."""
    
    # Environment
    environment: str = Field(default="development")
    debug: bool = Field(default=True)
    
    # Component configurations
    binance: BinanceConfig = Field(default_factory=BinanceConfig)
    risk: RiskConfig = Field(default_factory=RiskConfig)
    strategy: StrategyConfig = Field(default_factory=StrategyConfig)
    data: DataConfig = Field(default_factory=DataConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    # System settings
    trader_id: str = Field(default="FUNDING_ARBITRAGE_001")
    instance_id: str = Field(default="001")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
    
    @classmethod
    def from_yaml(cls, file_path: Path) -> "Config":
        """Load configuration from YAML file."""
        import yaml
        
        with open(file_path, "r") as f:
            data = yaml.safe_load(f)
        
        return cls(**data)
    
    def to_yaml(self, file_path: Path) -> None:
        """Save configuration to YAML file."""
        import yaml
        
        with open(file_path, "w") as f:
            yaml.dump(self.dict(), f, default_flow_style=False)
    
    def get_nautilus_config(self) -> Dict:
        """Get NautilusTrader configuration dictionary."""
        return {
            "trader_id": self.trader_id,
            "instance_id": self.instance_id,
            "logging": {
                "log_level": self.logging.level,
            },
            "data_clients": {
                "BINANCE": {
                    "api_key": self.binance.api_key,
                    "api_secret": self.binance.api_secret,
                    "account_type": "spot",
                    "testnet": self.binance.testnet,
                    "base_url_http": self.binance.base_url_http,
                    "base_url_ws": self.binance.base_url_ws,
                    "us": self.binance.us,
                }
            },
            "exec_clients": {
                "BINANCE": {
                    "api_key": self.binance.api_key,
                    "api_secret": self.binance.api_secret,
                    "account_type": "spot",
                    "testnet": self.binance.testnet,
                    "base_url_http": self.binance.base_url_http,
                    "base_url_ws": self.binance.base_url_ws,
                    "us": self.binance.us,
                }
            }
        }

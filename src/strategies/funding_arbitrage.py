"""
Funding rate arbitrage trading strategy.

This module implements the main trading strategy that executes funding rate
arbitrage by simultaneously trading spot and perpetual futures positions.
"""

from decimal import Decimal
from datetime import datetime, timezone
from typing import Dict, Optional

import pandas as pd
from nautilus_trader.config import StrategyConfig
from nautilus_trader.core.data import Data
from nautilus_trader.model.data import Bar, QuoteTick, TradeTick
from nautilus_trader.model.enums import OrderSide, TimeInForce
from nautilus_trader.model.events import OrderFilled
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.model.orders import MarketOrder, LimitOrder
from nautilus_trader.trading.strategy import Strategy

from src.strategies.analysis_engine import (
    FundingRateAnalysisEngine,
    ArbitrageSignal,
    SignalType,
)
from src.data.funding_rate import FundingRateData, FundingRateProvider
from src.data.market_data import MarketDataManager
from src.risk.manager import RiskManager, RiskEvent, RiskLevel
from src.risk.position_sizer import PositionSizer, PositionSizingMethod
from src.utils.logging import TradingLogger
from src.utils.time_utils import get_next_funding_time, time_to_funding


class FundingRateArbitrageConfig(StrategyConfig):
    """Configuration for funding rate arbitrage strategy."""

    instrument_id: InstrumentId
    max_position_size: Decimal = Decimal("1.0")
    min_funding_rate_threshold: Decimal = Decimal("0.01")
    min_arbitrage_profit: Decimal = Decimal("0.005")
    max_spread_threshold: Decimal = Decimal("0.02")
    position_size_percentage: Decimal = Decimal("0.1")
    use_limit_orders: bool = True
    limit_order_offset_bps: int = 5  # 5 basis points

    # Risk management settings
    max_portfolio_exposure: Decimal = Decimal("10000.0")
    stop_loss_percentage: Decimal = Decimal("0.05")
    max_leverage: Decimal = Decimal("3.0")

    # Position sizing settings
    position_sizing_method: str = "percent_capital"
    volatility_target: Decimal = Decimal("0.02")

    # Timing settings
    min_time_to_funding: int = 1800  # 30 minutes
    max_holding_period: int = 28800  # 8 hours

    # Analysis settings
    funding_rate_check_interval: int = 300  # 5 minutes


class FundingRateArbitrageStrategy(Strategy):
    """
    Funding rate arbitrage strategy implementation.
    
    This strategy monitors funding rates and executes arbitrage trades
    between spot and perpetual futures markets.
    """
    
    def __init__(self, config: FundingRateArbitrageConfig) -> None:
        super().__init__(config)
        
        # Configuration
        self.instrument_id = config.instrument_id
        self.max_position_size = config.max_position_size
        self.min_funding_rate_threshold = config.min_funding_rate_threshold
        self.min_arbitrage_profit = config.min_arbitrage_profit
        self.max_spread_threshold = config.max_spread_threshold
        self.position_size_percentage = config.position_size_percentage
        self.use_limit_orders = config.use_limit_orders
        self.limit_order_offset_bps = config.limit_order_offset_bps
        
        # State
        self.spot_instrument: Optional[Instrument] = None
        self.futures_instrument: Optional[Instrument] = None

        # Core components (will be initialized in on_start)
        self.analysis_engine: Optional[FundingRateAnalysisEngine] = None
        self.risk_manager: Optional[RiskManager] = None
        self.position_sizer: Optional[PositionSizer] = None
        self.market_data_manager: Optional[MarketDataManager] = None
        self.funding_rate_provider: Optional[FundingRateProvider] = None

        # Position tracking
        self.current_arbitrage_position: Optional[str] = None  # "long" or "short"
        self.spot_position_size: Decimal = Decimal("0")
        self.futures_position_size: Decimal = Decimal("0")
        self.position_entry_time: Optional[datetime] = None
        self.position_entry_funding_rate: Optional[Decimal] = None

        # Performance tracking
        self.total_funding_received: Decimal = Decimal("0")
        self.total_trading_pnl: Decimal = Decimal("0")
        self.trade_count: int = 0
        self.successful_trades: int = 0
        self.failed_trades: int = 0

        # Timing and state
        self.last_analysis_time: Optional[datetime] = None
        self.last_funding_check_time: Optional[datetime] = None

        # Logger
        self._logger = TradingLogger("funding_arbitrage_strategy")
    
    def on_start(self) -> None:
        """Actions to be performed on strategy start."""
        # Get instruments
        symbol = self.instrument_id.symbol.value

        # Spot instrument
        spot_instrument_id = InstrumentId.from_str(f"{symbol}.BINANCE")
        self.spot_instrument = self.cache.instrument(spot_instrument_id)

        # Futures instrument
        futures_instrument_id = InstrumentId.from_str(f"{symbol}-PERP.BINANCE")
        self.futures_instrument = self.cache.instrument(futures_instrument_id)

        if not self.spot_instrument or not self.futures_instrument:
            self.log.error(f"Could not find instruments for {symbol}")
            self.stop()
            return

        # Initialize core components
        self._initialize_components()

        # Subscribe to market data
        self.subscribe_quote_ticks(spot_instrument_id)
        self.subscribe_quote_ticks(futures_instrument_id)
        self.subscribe_trade_ticks(spot_instrument_id)
        self.subscribe_trade_ticks(futures_instrument_id)

        # Subscribe to funding rate data
        self.subscribe_data(
            data_type=FundingRateData,
            client_id=self.client_id,
        )

        # Subscribe to custom data types
        from nautilus_trader.adapters.binance.futures.types import BinanceFuturesMarkPriceUpdate
        self.subscribe_data(
            data_type=BinanceFuturesMarkPriceUpdate,
            client_id=self.client_id,
        )

        # Set up periodic analysis timer
        self.clock.set_timer(
            name="funding_analysis_timer",
            interval=pd.Timedelta(seconds=self.funding_rate_check_interval),
        )

        # Initialize timing
        self.last_analysis_time = self.clock.utc_now()
        self.last_funding_check_time = self.clock.utc_now()

        self.log.info(f"Strategy started for {symbol}")
        self._logger.logger.info(
            "Funding rate arbitrage strategy initialized",
            symbol=symbol,
            min_funding_rate_threshold=float(self.min_funding_rate_threshold),
            min_arbitrage_profit=float(self.min_arbitrage_profit),
        )
    
    def on_stop(self) -> None:
        """Actions to be performed on strategy stop."""
        # Close any open positions
        self._close_all_positions()
        
        # Log final performance
        self._log_performance_summary()
        
        self.log.info("Strategy stopped")

    def _initialize_components(self) -> None:
        """Initialize core strategy components."""
        # Initialize risk manager
        self.risk_manager = RiskManager(
            portfolio=self.portfolio,
            max_position_size=self.max_position_size,
            max_portfolio_exposure=self.max_portfolio_exposure,
            max_leverage=self.max_leverage,
            stop_loss_percentage=self.stop_loss_percentage,
        )

        # Initialize position sizer
        self.position_sizer = PositionSizer(
            portfolio=self.portfolio,
            default_method=PositionSizingMethod(self.position_sizing_method),
            default_size=self.position_size_percentage,
            max_position_size=self.max_position_size,
            volatility_target=self.volatility_target,
        )

        # Set position sizing method for our instrument
        symbol = self.instrument_id.symbol.value
        self.position_sizer.set_method_config(
            symbol=symbol,
            method=PositionSizingMethod(self.position_sizing_method),
            size=self.position_size_percentage,
        )

        self.log.info("Core components initialized")

    def _check_risk_before_trade(self, signal: ArbitrageSignal) -> bool:
        """Perform risk checks before executing a trade."""
        if not self.risk_manager:
            return True  # No risk manager, allow trade

        # Check emergency stop conditions
        if self.risk_manager.check_emergency_stop_conditions():
            self.log.error("Emergency stop active, blocking trade")
            return False

        # Check if we're too close to funding time
        if signal.time_to_funding.total_seconds() < self.min_time_to_funding:
            self.log.warning(f"Too close to funding time: {signal.time_to_funding}")
            return False

        # Check maximum holding period for existing positions
        if (self.current_arbitrage_position and self.position_entry_time and
            (self.clock.utc_now().to_pydatetime() - self.position_entry_time).total_seconds() > self.max_holding_period):
            self.log.info("Maximum holding period reached, will close position")
            self._close_arbitrage_position()

        return True

    def _calculate_optimal_position_size(self, signal: ArbitrageSignal) -> Decimal:
        """Calculate optimal position size using position sizer."""
        if not self.position_sizer:
            # Fallback to simple percentage calculation
            return self._calculate_position_size(signal.futures_price)

        symbol = signal.symbol
        current_price = signal.futures_price

        # Use signal confidence as signal strength
        signal_strength = signal.confidence

        # Calculate expected return and volatility for advanced sizing methods
        expected_return = signal.expected_profit
        expected_volatility = None  # Could be calculated from historical data

        position_size = self.position_sizer.calculate_position_size(
            symbol=symbol,
            current_price=current_price,
            signal_strength=signal_strength,
            expected_return=expected_return,
            expected_volatility=expected_volatility,
        )

        return position_size

    def _final_risk_check(self, symbol: str, position_size: Decimal, price: Decimal) -> bool:
        """Perform final risk check with calculated position size."""
        if not self.risk_manager:
            return True

        is_allowed, risk_event = self.risk_manager.check_position_risk(
            symbol=symbol,
            proposed_size=position_size,
            current_price=price,
        )

        if not is_allowed and risk_event:
            self.log.warning(f"Risk check failed: {risk_event.message}")
            self.risk_manager.add_risk_event(risk_event)
            return False

        return True

    def _execute_arbitrage_trades(
        self,
        spot_side: OrderSide,
        futures_side: OrderSide,
        position_size: Decimal,
        signal: ArbitrageSignal,
    ) -> bool:
        """Execute both legs of the arbitrage trade."""
        try:
            # Execute spot order
            spot_success = self._execute_spot_order(spot_side, position_size, signal.spot_price)

            if not spot_success:
                self.log.error("Failed to execute spot order")
                return False

            # Execute futures order
            futures_success = self._execute_futures_order(futures_side, position_size, signal.futures_price)

            if not futures_success:
                self.log.error("Failed to execute futures order, will attempt to close spot position")
                # Attempt to close the spot position
                self._execute_spot_order(
                    OrderSide.SELL if spot_side == OrderSide.BUY else OrderSide.BUY,
                    position_size,
                )
                return False

            return True

        except Exception as e:
            self.log.error(f"Error executing arbitrage trades: {e}")
            return False

    def _check_arbitrage_opportunity(self) -> None:
        """Check for arbitrage opportunities and execute if conditions are met."""
        current_time = self.clock.utc_now()

        # Throttle analysis to avoid excessive computation
        if (self.last_analysis_time and
            (current_time - self.last_analysis_time).total_seconds() < 60):  # 1 minute throttle
            return

        self.last_analysis_time = current_time

        # Check if we have all required data
        symbol = self.instrument_id.symbol.value
        spot_quote = self.cache.quote_tick(self.spot_instrument.id)
        futures_quote = self.cache.quote_tick(self.futures_instrument.id)

        if not spot_quote or not futures_quote:
            return

        # Get current funding rate (this would come from our data feed)
        # For now, we'll simulate this check
        funding_rate_data = self._get_current_funding_rate()
        if not funding_rate_data:
            return

        # Analyze arbitrage opportunity
        signal = self._analyze_arbitrage_opportunity(
            spot_quote, futures_quote, funding_rate_data
        )

        if signal:
            self._handle_arbitrage_signal(signal)

    def _get_current_funding_rate(self) -> Optional[FundingRateData]:
        """Get current funding rate data."""
        # This would typically come from our funding rate provider
        # For now, return None as placeholder
        return None

    def _analyze_arbitrage_opportunity(
        self,
        spot_quote: QuoteTick,
        futures_quote: QuoteTick,
        funding_rate_data: FundingRateData,
    ) -> Optional[ArbitrageSignal]:
        """Analyze current market conditions for arbitrage opportunities."""
        # Calculate prices and spread
        spot_mid = (spot_quote.bid_price + spot_quote.ask_price) / 2
        futures_mid = (futures_quote.bid_price + futures_quote.ask_price) / 2
        spread = futures_mid - spot_mid
        spread_percentage = (spread / spot_mid) * 100

        # Check spread threshold
        if abs(spread_percentage) > float(self.max_spread_threshold):
            return None  # Spread too wide

        # Check time to funding
        current_time = self.clock.utc_now().to_pydatetime()
        next_funding = get_next_funding_time(current_time)
        time_to_next_funding = time_to_funding(current_time)

        if time_to_next_funding.total_seconds() < self.min_time_to_funding:
            return None  # Too close to funding time

        # Determine signal type
        funding_rate = funding_rate_data.funding_rate
        signal_type = SignalType.NO_SIGNAL
        expected_profit = Decimal("0")
        confidence = Decimal("0")

        if funding_rate > self.min_funding_rate_threshold:
            # Positive funding rate: shorts pay longs
            # Strategy: Short futures, long spot
            signal_type = SignalType.LONG_ARBITRAGE
            expected_profit = funding_rate  # Simplified calculation
            confidence = min(Decimal("1.0"), funding_rate / self.min_funding_rate_threshold)
        elif funding_rate < -self.min_funding_rate_threshold:
            # Negative funding rate: longs pay shorts
            # Strategy: Long futures, short spot
            signal_type = SignalType.SHORT_ARBITRAGE
            expected_profit = abs(funding_rate)  # Simplified calculation
            confidence = min(Decimal("1.0"), abs(funding_rate) / self.min_funding_rate_threshold)

        if expected_profit < self.min_arbitrage_profit:
            return None  # Insufficient profit potential

        # Create arbitrage signal
        return ArbitrageSignal(
            symbol=self.instrument_id.symbol.value,
            signal_type=signal_type,
            funding_rate=funding_rate,
            spot_price=Decimal(str(spot_mid)),
            futures_price=Decimal(str(futures_mid)),
            spread=Decimal(str(spread_percentage)),
            expected_profit=expected_profit,
            confidence=confidence,
            timestamp=current_time,
            next_funding_time=next_funding,
            time_to_funding=time_to_next_funding,
        )

    def _handle_mark_price_update(self, update) -> None:
        """Handle Binance mark price updates."""
        # Extract funding rate information if available
        # This is where we would process real funding rate data
        pass
    
    def on_data(self, data: Data) -> None:
        """Handle custom data updates."""
        if isinstance(data, FundingRateData):
            self._handle_funding_rate_update(data)
        elif isinstance(data, ArbitrageSignal):
            self._handle_arbitrage_signal(data)
        else:
            # Handle Binance-specific data
            from nautilus_trader.adapters.binance.futures.types import BinanceFuturesMarkPriceUpdate
            if isinstance(data, BinanceFuturesMarkPriceUpdate):
                self._handle_mark_price_update(data)
    
    def on_quote_tick(self, tick: QuoteTick) -> None:
        """Handle quote tick updates."""
        # Update position sizer with current prices
        if self.position_sizer:
            symbol = tick.instrument_id.symbol.value
            if "PERP" in str(tick.instrument_id):
                symbol = symbol.replace("-PERP", "")

            mid_price = (tick.bid_price + tick.ask_price) / 2
            self.position_sizer.update_price_history(symbol, Decimal(str(mid_price)))

        # Check for arbitrage opportunities periodically
        self._check_arbitrage_opportunity()
    
    def on_trade_tick(self, tick: TradeTick) -> None:
        """Handle trade tick updates."""
        # Update internal state if needed
        pass
    
    def on_order_filled(self, event: OrderFilled) -> None:
        """Handle order fill events."""
        self._logger.log_order_event(
            symbol=event.instrument_id.symbol.value,
            order_type=event.order_type.name,
            side=event.order_side.name,
            quantity=float(event.last_qty),
            price=float(event.last_px),
            status="FILLED",
            order_id=str(event.client_order_id),
        )
        
        # Update position tracking
        self._update_position_tracking(event)
    
    def _handle_funding_rate_update(self, data: FundingRateData) -> None:
        """Handle funding rate data updates."""
        symbol = data.instrument_id.symbol.value.replace("-PERP", "")
        
        if symbol == self.instrument_id.symbol.value:
            self._logger.log_funding_payment(
                symbol=symbol,
                funding_rate=float(data.funding_rate),
                position_size=float(self.futures_position_size),
                payment=float(data.funding_rate * self.futures_position_size * (data.mark_price or Decimal("0"))),
            )
    
    def _handle_arbitrage_signal(self, signal: ArbitrageSignal) -> None:
        """Handle arbitrage signals from the analysis engine."""
        if signal.symbol != self.instrument_id.symbol.value:
            return
        
        if signal.signal_type == SignalType.LONG_ARBITRAGE:
            self._execute_long_arbitrage(signal)
        elif signal.signal_type == SignalType.SHORT_ARBITRAGE:
            self._execute_short_arbitrage(signal)
        elif signal.signal_type == SignalType.CLOSE_POSITION:
            self._close_arbitrage_position()
    
    def _execute_long_arbitrage(self, signal: ArbitrageSignal) -> None:
        """
        Execute long arbitrage strategy.

        Long arbitrage: Long spot, short futures (collect positive funding).
        """
        if self.current_arbitrage_position == "long":
            return  # Already in position

        # Close existing position if any
        if self.current_arbitrage_position:
            self._close_arbitrage_position()

        # Risk management checks
        if not self._check_risk_before_trade(signal):
            return

        # Calculate position size using position sizer
        position_size = self._calculate_optimal_position_size(signal)

        if position_size <= 0:
            self.log.warning("Position size calculation returned zero or negative")
            return

        # Final risk check with calculated position size
        if not self._final_risk_check(signal.symbol, position_size, signal.futures_price):
            return

        # Execute trades
        success = self._execute_arbitrage_trades(
            spot_side=OrderSide.BUY,
            futures_side=OrderSide.SELL,
            position_size=position_size,
            signal=signal,
        )

        if success:
            self.current_arbitrage_position = "long"
            self.position_entry_time = self.clock.utc_now().to_pydatetime()
            self.position_entry_funding_rate = signal.funding_rate
            self.trade_count += 1

            self.log.info(
                f"Executed long arbitrage for {signal.symbol}: "
                f"size={position_size}, funding_rate={signal.funding_rate}, "
                f"expected_profit={signal.expected_profit}"
            )

            self._logger.log_trade_signal(
                symbol=signal.symbol,
                signal_type="long_arbitrage_executed",
                funding_rate=float(signal.funding_rate),
                spread=float(signal.spread),
                confidence=float(signal.confidence),
                position_size=float(position_size),
            )
    
    def _execute_short_arbitrage(self, signal: ArbitrageSignal) -> None:
        """
        Execute short arbitrage strategy.
        
        Short arbitrage: Short spot, long futures (collect negative funding).
        """
        if self.current_arbitrage_position == "short":
            return  # Already in position
        
        # Close existing position if any
        if self.current_arbitrage_position:
            self._close_arbitrage_position()
        
        # Calculate position size
        position_size = self._calculate_position_size(signal.futures_price)
        
        if position_size <= 0:
            return
        
        # Execute trades
        self._execute_spot_order(OrderSide.SELL, position_size, signal.spot_price)
        self._execute_futures_order(OrderSide.BUY, position_size, signal.futures_price)
        
        self.current_arbitrage_position = "short"
        self.trade_count += 1
        
        self.log.info(
            f"Executed short arbitrage for {signal.symbol}: "
            f"funding_rate={signal.funding_rate}, "
            f"expected_profit={signal.expected_profit}"
        )
    
    def _close_arbitrage_position(self) -> None:
        """Close current arbitrage position."""
        if not self.current_arbitrage_position:
            return
        
        # Close spot position
        if self.spot_position_size != 0:
            side = OrderSide.SELL if self.spot_position_size > 0 else OrderSide.BUY
            self._execute_spot_order(side, abs(self.spot_position_size))
        
        # Close futures position
        if self.futures_position_size != 0:
            side = OrderSide.SELL if self.futures_position_size > 0 else OrderSide.BUY
            self._execute_futures_order(side, abs(self.futures_position_size))
        
        self.log.info(f"Closed arbitrage position: {self.current_arbitrage_position}")
        self.current_arbitrage_position = None

    def _execute_spot_order(
        self,
        side: OrderSide,
        quantity: Decimal,
        reference_price: Optional[Decimal] = None,
    ) -> bool:
        """Execute spot market order."""
        if not self.spot_instrument:
            return False

        try:
            order_quantity = self.spot_instrument.make_qty(quantity)

            if self.use_limit_orders and reference_price:
                # Calculate limit price with offset
                offset = reference_price * Decimal(str(self.limit_order_offset_bps)) / Decimal("10000")
                if side == OrderSide.BUY:
                    limit_price = reference_price + offset
                else:
                    limit_price = reference_price - offset

                order = self.order_factory.limit(
                    instrument_id=self.spot_instrument.id,
                    order_side=side,
                    quantity=order_quantity,
                    price=self.spot_instrument.make_price(limit_price),
                    time_in_force=TimeInForce.GTC,
                )
            else:
                order = self.order_factory.market(
                    instrument_id=self.spot_instrument.id,
                    order_side=side,
                    quantity=order_quantity,
                )

            self.submit_order(order)
            return True

        except Exception as e:
            self.log.error(f"Error executing spot order: {e}")
            return False

    def _execute_futures_order(
        self,
        side: OrderSide,
        quantity: Decimal,
        reference_price: Optional[Decimal] = None,
    ) -> bool:
        """Execute futures market order."""
        if not self.futures_instrument:
            return False

        try:
            order_quantity = self.futures_instrument.make_qty(quantity)

            if self.use_limit_orders and reference_price:
                # Calculate limit price with offset
                offset = reference_price * Decimal(str(self.limit_order_offset_bps)) / Decimal("10000")
                if side == OrderSide.BUY:
                    limit_price = reference_price + offset
                else:
                    limit_price = reference_price - offset

                order = self.order_factory.limit(
                    instrument_id=self.futures_instrument.id,
                    order_side=side,
                    quantity=order_quantity,
                    price=self.futures_instrument.make_price(limit_price),
                    time_in_force=TimeInForce.GTC,
                )
            else:
                order = self.order_factory.market(
                    instrument_id=self.futures_instrument.id,
                    order_side=side,
                    quantity=order_quantity,
                )

            self.submit_order(order)
            return True

        except Exception as e:
            self.log.error(f"Error executing futures order: {e}")
            return False

    def _calculate_position_size(self, reference_price: Decimal) -> Decimal:
        """Calculate position size based on risk parameters."""
        # Get available balance
        account = self.portfolio.account(self.futures_instrument.venue)
        if not account:
            return Decimal("0")

        available_balance = account.balance_total().as_decimal()

        # Calculate position size based on percentage of balance
        position_value = available_balance * self.position_size_percentage
        position_size = position_value / reference_price

        # Apply maximum position size limit
        if position_size > self.max_position_size:
            position_size = self.max_position_size

        return position_size

    def _update_position_tracking(self, event: OrderFilled) -> None:
        """Update position tracking based on order fills."""
        quantity = event.last_qty
        if event.order_side == OrderSide.SELL:
            quantity = -quantity

        if "PERP" in str(event.instrument_id):
            self.futures_position_size += quantity
        else:
            self.spot_position_size += quantity

    def _close_all_positions(self) -> None:
        """Close all open positions."""
        self._close_arbitrage_position()

    def _log_performance_summary(self) -> None:
        """Log performance summary."""
        self._logger.log_performance_metrics(
            period="session",
            total_pnl=float(self.total_trading_pnl + self.total_funding_received),
            funding_pnl=float(self.total_funding_received),
            trading_pnl=float(self.total_trading_pnl),
            sharpe_ratio=0.0,  # TODO: Calculate
            max_drawdown=0.0,  # TODO: Calculate
            trade_count=self.trade_count,
        )

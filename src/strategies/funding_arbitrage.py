"""
Funding rate arbitrage trading strategy.

This module implements the main trading strategy that executes funding rate
arbitrage by simultaneously trading spot and perpetual futures positions.
"""

from decimal import Decimal
from typing import Dict, Optional

from nautilus_trader.config import StrategyConfig
from nautilus_trader.core.data import Data
from nautilus_trader.model.data import Bar, QuoteTick, TradeTick
from nautilus_trader.model.enums import OrderSide, TimeInForce
from nautilus_trader.model.events import OrderFilled
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments import Instrument
from nautilus_trader.model.orders import MarketOrder, LimitOrder
from nautilus_trader.trading.strategy import Strategy

from src.strategies.analysis_engine import (
    FundingRateAnalysisEngine,
    ArbitrageSignal,
    SignalType,
)
from src.data.funding_rate import FundingRateData
from src.utils.logging import TradingLogger


class FundingRateArbitrageConfig(StrategyConfig):
    """Configuration for funding rate arbitrage strategy."""
    
    instrument_id: InstrumentId
    max_position_size: Decimal = Decimal("1.0")
    min_funding_rate_threshold: Decimal = Decimal("0.01")
    min_arbitrage_profit: Decimal = Decimal("0.005")
    max_spread_threshold: Decimal = Decimal("0.02")
    position_size_percentage: Decimal = Decimal("0.1")
    use_limit_orders: bool = True
    limit_order_offset_bps: int = 5  # 5 basis points


class FundingRateArbitrageStrategy(Strategy):
    """
    Funding rate arbitrage strategy implementation.
    
    This strategy monitors funding rates and executes arbitrage trades
    between spot and perpetual futures markets.
    """
    
    def __init__(self, config: FundingRateArbitrageConfig) -> None:
        super().__init__(config)
        
        # Configuration
        self.instrument_id = config.instrument_id
        self.max_position_size = config.max_position_size
        self.min_funding_rate_threshold = config.min_funding_rate_threshold
        self.min_arbitrage_profit = config.min_arbitrage_profit
        self.max_spread_threshold = config.max_spread_threshold
        self.position_size_percentage = config.position_size_percentage
        self.use_limit_orders = config.use_limit_orders
        self.limit_order_offset_bps = config.limit_order_offset_bps
        
        # State
        self.spot_instrument: Optional[Instrument] = None
        self.futures_instrument: Optional[Instrument] = None
        self.analysis_engine: Optional[FundingRateAnalysisEngine] = None
        
        # Position tracking
        self.current_arbitrage_position: Optional[str] = None  # "long" or "short"
        self.spot_position_size: Decimal = Decimal("0")
        self.futures_position_size: Decimal = Decimal("0")
        
        # Performance tracking
        self.total_funding_received: Decimal = Decimal("0")
        self.total_trading_pnl: Decimal = Decimal("0")
        self.trade_count: int = 0
        
        # Logger
        self._logger = TradingLogger("funding_arbitrage_strategy")
    
    def on_start(self) -> None:
        """Actions to be performed on strategy start."""
        # Get instruments
        symbol = self.instrument_id.symbol.value
        
        # Spot instrument
        spot_instrument_id = InstrumentId.from_str(f"{symbol}.BINANCE")
        self.spot_instrument = self.cache.instrument(spot_instrument_id)
        
        # Futures instrument  
        futures_instrument_id = InstrumentId.from_str(f"{symbol}-PERP.BINANCE")
        self.futures_instrument = self.cache.instrument(futures_instrument_id)
        
        if not self.spot_instrument or not self.futures_instrument:
            self.log.error(f"Could not find instruments for {symbol}")
            self.stop()
            return
        
        # Subscribe to market data
        self.subscribe_quote_ticks(spot_instrument_id)
        self.subscribe_quote_ticks(futures_instrument_id)
        self.subscribe_trade_ticks(spot_instrument_id)
        self.subscribe_trade_ticks(futures_instrument_id)
        
        # Subscribe to funding rate data
        self.subscribe_data(
            data_type=FundingRateData,
            client_id=self.client_id,
        )
        
        self.log.info(f"Strategy started for {symbol}")
    
    def on_stop(self) -> None:
        """Actions to be performed on strategy stop."""
        # Close any open positions
        self._close_all_positions()
        
        # Log final performance
        self._log_performance_summary()
        
        self.log.info("Strategy stopped")
    
    def on_data(self, data: Data) -> None:
        """Handle custom data updates."""
        if isinstance(data, FundingRateData):
            self._handle_funding_rate_update(data)
        elif isinstance(data, ArbitrageSignal):
            self._handle_arbitrage_signal(data)
    
    def on_quote_tick(self, tick: QuoteTick) -> None:
        """Handle quote tick updates."""
        # Update internal state if needed
        pass
    
    def on_trade_tick(self, tick: TradeTick) -> None:
        """Handle trade tick updates."""
        # Update internal state if needed
        pass
    
    def on_order_filled(self, event: OrderFilled) -> None:
        """Handle order fill events."""
        self._logger.log_order_event(
            symbol=event.instrument_id.symbol.value,
            order_type=event.order_type.name,
            side=event.order_side.name,
            quantity=float(event.last_qty),
            price=float(event.last_px),
            status="FILLED",
            order_id=str(event.client_order_id),
        )
        
        # Update position tracking
        self._update_position_tracking(event)
    
    def _handle_funding_rate_update(self, data: FundingRateData) -> None:
        """Handle funding rate data updates."""
        symbol = data.instrument_id.symbol.value.replace("-PERP", "")
        
        if symbol == self.instrument_id.symbol.value:
            self._logger.log_funding_payment(
                symbol=symbol,
                funding_rate=float(data.funding_rate),
                position_size=float(self.futures_position_size),
                payment=float(data.funding_rate * self.futures_position_size * (data.mark_price or Decimal("0"))),
            )
    
    def _handle_arbitrage_signal(self, signal: ArbitrageSignal) -> None:
        """Handle arbitrage signals from the analysis engine."""
        if signal.symbol != self.instrument_id.symbol.value:
            return
        
        if signal.signal_type == SignalType.LONG_ARBITRAGE:
            self._execute_long_arbitrage(signal)
        elif signal.signal_type == SignalType.SHORT_ARBITRAGE:
            self._execute_short_arbitrage(signal)
        elif signal.signal_type == SignalType.CLOSE_POSITION:
            self._close_arbitrage_position()
    
    def _execute_long_arbitrage(self, signal: ArbitrageSignal) -> None:
        """
        Execute long arbitrage strategy.
        
        Long arbitrage: Long spot, short futures (collect positive funding).
        """
        if self.current_arbitrage_position == "long":
            return  # Already in position
        
        # Close existing position if any
        if self.current_arbitrage_position:
            self._close_arbitrage_position()
        
        # Calculate position size
        position_size = self._calculate_position_size(signal.futures_price)
        
        if position_size <= 0:
            return
        
        # Execute trades
        self._execute_spot_order(OrderSide.BUY, position_size, signal.spot_price)
        self._execute_futures_order(OrderSide.SELL, position_size, signal.futures_price)
        
        self.current_arbitrage_position = "long"
        self.trade_count += 1
        
        self.log.info(
            f"Executed long arbitrage for {signal.symbol}: "
            f"funding_rate={signal.funding_rate}, "
            f"expected_profit={signal.expected_profit}"
        )
    
    def _execute_short_arbitrage(self, signal: ArbitrageSignal) -> None:
        """
        Execute short arbitrage strategy.
        
        Short arbitrage: Short spot, long futures (collect negative funding).
        """
        if self.current_arbitrage_position == "short":
            return  # Already in position
        
        # Close existing position if any
        if self.current_arbitrage_position:
            self._close_arbitrage_position()
        
        # Calculate position size
        position_size = self._calculate_position_size(signal.futures_price)
        
        if position_size <= 0:
            return
        
        # Execute trades
        self._execute_spot_order(OrderSide.SELL, position_size, signal.spot_price)
        self._execute_futures_order(OrderSide.BUY, position_size, signal.futures_price)
        
        self.current_arbitrage_position = "short"
        self.trade_count += 1
        
        self.log.info(
            f"Executed short arbitrage for {signal.symbol}: "
            f"funding_rate={signal.funding_rate}, "
            f"expected_profit={signal.expected_profit}"
        )
    
    def _close_arbitrage_position(self) -> None:
        """Close current arbitrage position."""
        if not self.current_arbitrage_position:
            return
        
        # Close spot position
        if self.spot_position_size != 0:
            side = OrderSide.SELL if self.spot_position_size > 0 else OrderSide.BUY
            self._execute_spot_order(side, abs(self.spot_position_size))
        
        # Close futures position
        if self.futures_position_size != 0:
            side = OrderSide.SELL if self.futures_position_size > 0 else OrderSide.BUY
            self._execute_futures_order(side, abs(self.futures_position_size))
        
        self.log.info(f"Closed arbitrage position: {self.current_arbitrage_position}")
        self.current_arbitrage_position = None

    def _execute_spot_order(
        self,
        side: OrderSide,
        quantity: Decimal,
        reference_price: Optional[Decimal] = None,
    ) -> None:
        """Execute spot market order."""
        if not self.spot_instrument:
            return

        order_quantity = self.spot_instrument.make_qty(quantity)

        if self.use_limit_orders and reference_price:
            # Calculate limit price with offset
            offset = reference_price * Decimal(str(self.limit_order_offset_bps)) / Decimal("10000")
            if side == OrderSide.BUY:
                limit_price = reference_price + offset
            else:
                limit_price = reference_price - offset

            order = self.order_factory.limit(
                instrument_id=self.spot_instrument.id,
                order_side=side,
                quantity=order_quantity,
                price=self.spot_instrument.make_price(limit_price),
                time_in_force=TimeInForce.GTC,
            )
        else:
            order = self.order_factory.market(
                instrument_id=self.spot_instrument.id,
                order_side=side,
                quantity=order_quantity,
            )

        self.submit_order(order)

    def _execute_futures_order(
        self,
        side: OrderSide,
        quantity: Decimal,
        reference_price: Optional[Decimal] = None,
    ) -> None:
        """Execute futures market order."""
        if not self.futures_instrument:
            return

        order_quantity = self.futures_instrument.make_qty(quantity)

        if self.use_limit_orders and reference_price:
            # Calculate limit price with offset
            offset = reference_price * Decimal(str(self.limit_order_offset_bps)) / Decimal("10000")
            if side == OrderSide.BUY:
                limit_price = reference_price + offset
            else:
                limit_price = reference_price - offset

            order = self.order_factory.limit(
                instrument_id=self.futures_instrument.id,
                order_side=side,
                quantity=order_quantity,
                price=self.futures_instrument.make_price(limit_price),
                time_in_force=TimeInForce.GTC,
            )
        else:
            order = self.order_factory.market(
                instrument_id=self.futures_instrument.id,
                order_side=side,
                quantity=order_quantity,
            )

        self.submit_order(order)

    def _calculate_position_size(self, reference_price: Decimal) -> Decimal:
        """Calculate position size based on risk parameters."""
        # Get available balance
        account = self.portfolio.account(self.futures_instrument.venue)
        if not account:
            return Decimal("0")

        available_balance = account.balance_total().as_decimal()

        # Calculate position size based on percentage of balance
        position_value = available_balance * self.position_size_percentage
        position_size = position_value / reference_price

        # Apply maximum position size limit
        if position_size > self.max_position_size:
            position_size = self.max_position_size

        return position_size

    def _update_position_tracking(self, event: OrderFilled) -> None:
        """Update position tracking based on order fills."""
        quantity = event.last_qty
        if event.order_side == OrderSide.SELL:
            quantity = -quantity

        if "PERP" in str(event.instrument_id):
            self.futures_position_size += quantity
        else:
            self.spot_position_size += quantity

    def _close_all_positions(self) -> None:
        """Close all open positions."""
        self._close_arbitrage_position()

    def _log_performance_summary(self) -> None:
        """Log performance summary."""
        self._logger.log_performance_metrics(
            period="session",
            total_pnl=float(self.total_trading_pnl + self.total_funding_received),
            funding_pnl=float(self.total_funding_received),
            trading_pnl=float(self.total_trading_pnl),
            sharpe_ratio=0.0,  # TODO: Calculate
            max_drawdown=0.0,  # TODO: Calculate
            trade_count=self.trade_count,
        )

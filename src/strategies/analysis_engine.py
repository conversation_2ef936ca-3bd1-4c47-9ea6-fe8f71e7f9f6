"""
Funding rate analysis engine for identifying arbitrage opportunities.

This module provides the core analysis engine that monitors funding rates,
calculates arbitrage opportunities, and generates trading signals.
"""

import asyncio
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Callable, NamedTuple
from enum import Enum

import pandas as pd
from nautilus_trader.model.identifiers import InstrumentId

from src.data.funding_rate import FundingRateData, FundingRateProvider
from src.data.market_data import MarketDataManager
from src.utils.calculations import (
    calculate_funding_rate_profit,
    calculate_arbitrage_profit,
    calculate_break_even_funding_rate,
)
from src.utils.time_utils import get_next_funding_time, time_to_funding
from src.utils.logging import TradingLogger


class SignalType(Enum):
    """Types of arbitrage signals."""
    LONG_ARBITRAGE = "long_arbitrage"  # Long spot, short futures
    SHORT_ARBITRAGE = "short_arbitrage"  # Short spot, long futures
    CLOSE_POSITION = "close_position"
    NO_SIGNAL = "no_signal"


class ArbitrageSignal(NamedTuple):
    """Arbitrage trading signal."""
    symbol: str
    signal_type: SignalType
    funding_rate: Decimal
    spot_price: Decimal
    futures_price: Decimal
    spread: Decimal
    expected_profit: Decimal
    confidence: Decimal
    timestamp: datetime
    next_funding_time: datetime
    time_to_funding: timedelta


class FundingRateAnalysisEngine:
    """
    Core engine for analyzing funding rates and generating arbitrage signals.
    """
    
    def __init__(
        self,
        market_data_manager: MarketDataManager,
        funding_rate_provider: FundingRateProvider,
        instruments: List[str],
        min_funding_rate_threshold: Decimal = Decimal("0.01"),
        min_arbitrage_profit: Decimal = Decimal("0.005"),
        max_spread_threshold: Decimal = Decimal("0.02"),
        min_time_to_funding: int = 1800,  # 30 minutes
    ) -> None:
        self._market_data_manager = market_data_manager
        self._funding_rate_provider = funding_rate_provider
        self._instruments = instruments
        self._min_funding_rate_threshold = min_funding_rate_threshold
        self._min_arbitrage_profit = min_arbitrage_profit
        self._max_spread_threshold = max_spread_threshold
        self._min_time_to_funding = min_time_to_funding
        
        # Signal callbacks
        self._signal_callbacks: List[Callable[[ArbitrageSignal], None]] = []
        
        # Analysis state
        self._current_signals: Dict[str, ArbitrageSignal] = {}
        self._signal_history: List[ArbitrageSignal] = []
        self._analysis_running = False
        
        # Logger
        self._logger = TradingLogger("analysis_engine")
    
    def add_signal_callback(self, callback: Callable[[ArbitrageSignal], None]) -> None:
        """Add a callback for arbitrage signals."""
        self._signal_callbacks.append(callback)
    
    async def start(self) -> None:
        """Start the analysis engine."""
        if self._analysis_running:
            return
        
        self._analysis_running = True
        
        # Start periodic analysis
        asyncio.create_task(self._run_analysis_loop())
        
        self._logger.logger.info("Funding rate analysis engine started")
    
    async def stop(self) -> None:
        """Stop the analysis engine."""
        self._analysis_running = False
        self._logger.logger.info("Funding rate analysis engine stopped")
    
    async def _run_analysis_loop(self) -> None:
        """Main analysis loop."""
        while self._analysis_running:
            try:
                await self._analyze_all_instruments()
                await asyncio.sleep(60)  # Analyze every minute
            except Exception as e:
                self._logger.log_error("analysis_loop", str(e))
                await asyncio.sleep(30)  # Retry after 30 seconds on error
    
    async def _analyze_all_instruments(self) -> None:
        """Analyze all configured instruments for arbitrage opportunities."""
        for symbol in self._instruments:
            try:
                signal = await self._analyze_instrument(symbol)
                if signal and signal.signal_type != SignalType.NO_SIGNAL:
                    self._current_signals[symbol] = signal
                    self._signal_history.append(signal)
                    
                    # Notify callbacks
                    for callback in self._signal_callbacks:
                        callback(signal)
                    
                    # Log signal
                    self._logger.log_trade_signal(
                        symbol=signal.symbol,
                        signal_type=signal.signal_type.value,
                        funding_rate=float(signal.funding_rate),
                        spread=float(signal.spread),
                        confidence=float(signal.confidence),
                        expected_profit=float(signal.expected_profit),
                    )
            except Exception as e:
                self._logger.log_error("instrument_analysis", str(e), symbol=symbol)
    
    async def _analyze_instrument(self, symbol: str) -> Optional[ArbitrageSignal]:
        """
        Analyze a single instrument for arbitrage opportunities.
        
        Parameters
        ----------
        symbol : str
            The symbol to analyze.
        
        Returns
        -------
        ArbitrageSignal, optional
            Arbitrage signal if opportunity exists.
        """
        # Get market data
        spot_quote = self._market_data_manager.get_spot_quote(symbol)
        futures_quote = self._market_data_manager.get_futures_quote(symbol)
        funding_rate_data = self._market_data_manager.get_funding_rate(symbol)
        
        if not all([spot_quote, futures_quote, funding_rate_data]):
            return None
        
        # Calculate prices and spread
        spot_price = (spot_quote.bid_price + spot_quote.ask_price) / 2
        futures_price = (futures_quote.bid_price + futures_quote.ask_price) / 2
        spread = futures_price - spot_price
        spread_percentage = (spread / spot_price) * 100
        
        # Get funding rate
        funding_rate = funding_rate_data.funding_rate
        
        # Check time to funding
        current_time = datetime.now(timezone.utc)
        next_funding = get_next_funding_time(current_time)
        time_to_next_funding = time_to_funding(current_time)
        
        # Skip if too close to funding time
        if time_to_next_funding.total_seconds() < self._min_time_to_funding:
            return ArbitrageSignal(
                symbol=symbol,
                signal_type=SignalType.NO_SIGNAL,
                funding_rate=funding_rate,
                spot_price=spot_price,
                futures_price=futures_price,
                spread=spread_percentage,
                expected_profit=Decimal("0"),
                confidence=Decimal("0"),
                timestamp=current_time,
                next_funding_time=next_funding,
                time_to_funding=time_to_next_funding,
            )
        
        # Analyze arbitrage opportunity
        signal_type, expected_profit, confidence = self._evaluate_arbitrage_opportunity(
            symbol=symbol,
            funding_rate=funding_rate,
            spot_price=spot_price,
            futures_price=futures_price,
            spread_percentage=spread_percentage,
        )
        
        return ArbitrageSignal(
            symbol=symbol,
            signal_type=signal_type,
            funding_rate=funding_rate,
            spot_price=spot_price,
            futures_price=futures_price,
            spread=spread_percentage,
            expected_profit=expected_profit,
            confidence=confidence,
            timestamp=current_time,
            next_funding_time=next_funding,
            time_to_funding=time_to_next_funding,
        )
    
    def _evaluate_arbitrage_opportunity(
        self,
        symbol: str,
        funding_rate: Decimal,
        spot_price: Decimal,
        futures_price: Decimal,
        spread_percentage: Decimal,
    ) -> tuple[SignalType, Decimal, Decimal]:
        """
        Evaluate arbitrage opportunity for an instrument.
        
        Returns
        -------
        tuple[SignalType, Decimal, Decimal]
            (signal_type, expected_profit, confidence)
        """
        # Check if spread is too wide (market stress)
        if abs(spread_percentage) > self._max_spread_threshold:
            return SignalType.NO_SIGNAL, Decimal("0"), Decimal("0")
        
        # Calculate break-even funding rate
        break_even_rate = calculate_break_even_funding_rate(
            spot_price=spot_price,
            futures_price=futures_price,
            transaction_costs=Decimal("0.001"),  # 0.1% transaction costs
        )
        
        # Determine signal type based on funding rate
        signal_type = SignalType.NO_SIGNAL
        expected_profit = Decimal("0")
        confidence = Decimal("0")
        
        if funding_rate > self._min_funding_rate_threshold:
            # Positive funding rate: shorts pay longs
            # Strategy: Short futures, long spot (collect funding)
            if funding_rate > break_even_rate:
                signal_type = SignalType.LONG_ARBITRAGE
                
                # Calculate expected profit
                position_size = Decimal("1.0")  # Normalized position
                funding_profit, convergence_profit, total_profit = calculate_arbitrage_profit(
                    spot_price=spot_price,
                    futures_price=futures_price,
                    funding_rate=funding_rate,
                    position_size=position_size,
                )
                
                expected_profit = total_profit / (position_size * futures_price)  # As percentage
                
                # Calculate confidence based on funding rate magnitude
                confidence = min(
                    Decimal("1.0"),
                    (funding_rate - break_even_rate) / self._min_funding_rate_threshold
                )
        
        elif funding_rate < -self._min_funding_rate_threshold:
            # Negative funding rate: longs pay shorts
            # Strategy: Long futures, short spot (collect funding)
            if abs(funding_rate) > break_even_rate:
                signal_type = SignalType.SHORT_ARBITRAGE
                
                # Calculate expected profit (funding rate is negative)
                position_size = Decimal("1.0")
                funding_profit = position_size * futures_price * abs(funding_rate)
                convergence_profit = position_size * (spot_price - futures_price)
                transaction_costs = position_size * futures_price * Decimal("0.002")
                
                total_profit = funding_profit + convergence_profit - transaction_costs
                expected_profit = total_profit / (position_size * futures_price)
                
                # Calculate confidence
                confidence = min(
                    Decimal("1.0"),
                    (abs(funding_rate) - break_even_rate) / self._min_funding_rate_threshold
                )
        
        # Check minimum profit threshold
        if expected_profit < self._min_arbitrage_profit:
            signal_type = SignalType.NO_SIGNAL
            expected_profit = Decimal("0")
            confidence = Decimal("0")
        
        return signal_type, expected_profit, confidence
    
    def get_current_signal(self, symbol: str) -> Optional[ArbitrageSignal]:
        """Get current signal for a symbol."""
        return self._current_signals.get(symbol)
    
    def get_signal_history(
        self,
        symbol: Optional[str] = None,
        limit: Optional[int] = None,
    ) -> List[ArbitrageSignal]:
        """
        Get signal history.
        
        Parameters
        ----------
        symbol : str, optional
            Filter by symbol.
        limit : int, optional
            Limit number of results.
        
        Returns
        -------
        List[ArbitrageSignal]
            Signal history.
        """
        signals = self._signal_history
        
        if symbol:
            signals = [s for s in signals if s.symbol == symbol]
        
        if limit:
            signals = signals[-limit:]
        
        return signals
    
    def get_analysis_summary(self) -> Dict:
        """
        Get analysis summary.
        
        Returns
        -------
        Dict
            Analysis summary.
        """
        current_time = datetime.now(timezone.utc)
        
        summary = {
            "timestamp": current_time,
            "analysis_running": self._analysis_running,
            "instruments_analyzed": len(self._instruments),
            "active_signals": len([s for s in self._current_signals.values() 
                                 if s.signal_type != SignalType.NO_SIGNAL]),
            "total_signals_generated": len(self._signal_history),
            "current_signals": {},
        }
        
        for symbol, signal in self._current_signals.items():
            summary["current_signals"][symbol] = {
                "signal_type": signal.signal_type.value,
                "funding_rate": float(signal.funding_rate),
                "spread": float(signal.spread),
                "expected_profit": float(signal.expected_profit),
                "confidence": float(signal.confidence),
                "time_to_funding": str(signal.time_to_funding),
            }
        
        return summary

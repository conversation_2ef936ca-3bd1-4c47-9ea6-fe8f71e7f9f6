"""
Position tracking utilities for the funding rate arbitrage system.

This module provides detailed position tracking and analysis for arbitrage positions
across spot and futures markets.
"""

from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, NamedTuple, Tuple
from enum import Enum

import pandas as pd
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.position import Position
from nautilus_trader.model.events import OrderFilled
from nautilus_trader.model.enums import OrderSide

from src.utils.calculations import calculate_funding_rate_profit
from src.utils.time_utils import get_funding_times_in_range
from src.utils.logging import TradingLogger


class PositionStatus(Enum):
    """Position status enumeration."""
    OPENING = "opening"
    OPEN = "open"
    CLOSING = "closing"
    CLOSED = "closed"
    ERROR = "error"


class PositionLeg(NamedTuple):
    """Individual leg of an arbitrage position."""
    instrument_id: InstrumentId
    side: OrderSide
    size: Decimal
    entry_price: Decimal
    current_price: Optional[Decimal] = None
    unrealized_pnl: Decimal = Decimal("0")
    realized_pnl: Decimal = Decimal("0")
    fill_time: Optional[datetime] = None


class ArbitragePositionDetail(NamedTuple):
    """Detailed arbitrage position information."""
    position_id: str
    symbol: str
    arbitrage_type: str  # "long" or "short"
    status: PositionStatus
    spot_leg: PositionLeg
    futures_leg: PositionLeg
    entry_time: datetime
    close_time: Optional[datetime] = None
    entry_funding_rate: Decimal = Decimal("0")
    total_funding_received: Decimal = Decimal("0")
    total_unrealized_pnl: Decimal = Decimal("0")
    total_realized_pnl: Decimal = Decimal("0")
    hedge_ratio: Decimal = Decimal("1.0")
    expected_profit: Decimal = Decimal("0")
    actual_profit: Decimal = Decimal("0")


class PositionTracker:
    """
    Advanced position tracker for arbitrage positions.
    """
    
    def __init__(self) -> None:
        # Position tracking
        self._positions: Dict[str, ArbitragePositionDetail] = {}
        self._position_history: List[ArbitragePositionDetail] = []
        
        # Order tracking for position building
        self._pending_orders: Dict[str, OrderFilled] = {}
        self._position_orders: Dict[str, List[OrderFilled]] = {}
        
        # Performance tracking
        self._position_metrics: Dict[str, Dict] = {}
        
        # Logger
        self._logger = TradingLogger("position_tracker")
    
    def start_arbitrage_position(
        self,
        position_id: str,
        symbol: str,
        arbitrage_type: str,
        expected_profit: Decimal,
        entry_funding_rate: Decimal,
    ) -> None:
        """Start tracking a new arbitrage position."""
        # Create placeholder position
        spot_instrument_id = InstrumentId.from_str(f"{symbol}.BINANCE")
        futures_instrument_id = InstrumentId.from_str(f"{symbol}-PERP.BINANCE")
        
        # Determine sides based on arbitrage type
        if arbitrage_type == "long":
            spot_side = OrderSide.BUY
            futures_side = OrderSide.SELL
        else:
            spot_side = OrderSide.SELL
            futures_side = OrderSide.BUY
        
        spot_leg = PositionLeg(
            instrument_id=spot_instrument_id,
            side=spot_side,
            size=Decimal("0"),
            entry_price=Decimal("0"),
        )
        
        futures_leg = PositionLeg(
            instrument_id=futures_instrument_id,
            side=futures_side,
            size=Decimal("0"),
            entry_price=Decimal("0"),
        )
        
        position = ArbitragePositionDetail(
            position_id=position_id,
            symbol=symbol,
            arbitrage_type=arbitrage_type,
            status=PositionStatus.OPENING,
            spot_leg=spot_leg,
            futures_leg=futures_leg,
            entry_time=datetime.now(timezone.utc),
            entry_funding_rate=entry_funding_rate,
            expected_profit=expected_profit,
        )
        
        self._positions[position_id] = position
        self._position_orders[position_id] = []
        
        self._logger.logger.info(
            f"Started tracking arbitrage position {position_id}",
            symbol=symbol,
            arbitrage_type=arbitrage_type,
            expected_profit=float(expected_profit),
        )
    
    def add_order_fill(self, position_id: str, order_fill: OrderFilled) -> None:
        """Add an order fill to a position."""
        if position_id not in self._positions:
            self._logger.logger.warning(f"Position {position_id} not found for order fill")
            return
        
        position = self._positions[position_id]
        self._position_orders[position_id].append(order_fill)
        
        # Update position legs
        updated_position = self._update_position_with_fill(position, order_fill)
        self._positions[position_id] = updated_position
        
        # Check if position is fully opened
        if (updated_position.spot_leg.size > 0 and 
            updated_position.futures_leg.size > 0 and
            updated_position.status == PositionStatus.OPENING):
            
            # Mark position as open
            self._positions[position_id] = updated_position._replace(
                status=PositionStatus.OPEN
            )
            
            self._logger.logger.info(
                f"Arbitrage position {position_id} fully opened",
                spot_size=float(updated_position.spot_leg.size),
                futures_size=float(updated_position.futures_leg.size),
            )
    
    def update_position_prices(
        self,
        position_id: str,
        spot_price: Decimal,
        futures_price: Decimal,
    ) -> None:
        """Update current prices for a position."""
        if position_id not in self._positions:
            return
        
        position = self._positions[position_id]
        
        # Update leg prices and calculate unrealized PnL
        updated_spot_leg = self._update_leg_price(position.spot_leg, spot_price)
        updated_futures_leg = self._update_leg_price(position.futures_leg, futures_price)
        
        # Calculate total unrealized PnL
        total_unrealized_pnl = updated_spot_leg.unrealized_pnl + updated_futures_leg.unrealized_pnl
        
        # Update position
        updated_position = position._replace(
            spot_leg=updated_spot_leg,
            futures_leg=updated_futures_leg,
            total_unrealized_pnl=total_unrealized_pnl,
        )
        
        self._positions[position_id] = updated_position
    
    def close_arbitrage_position(
        self,
        position_id: str,
        close_spot_price: Decimal,
        close_futures_price: Decimal,
    ) -> Optional[ArbitragePositionDetail]:
        """Close an arbitrage position."""
        if position_id not in self._positions:
            return None
        
        position = self._positions[position_id]
        
        # Calculate final realized PnL
        spot_realized_pnl = self._calculate_leg_realized_pnl(
            position.spot_leg, close_spot_price
        )
        futures_realized_pnl = self._calculate_leg_realized_pnl(
            position.futures_leg, close_futures_price
        )
        
        total_realized_pnl = spot_realized_pnl + futures_realized_pnl
        actual_profit = total_realized_pnl + position.total_funding_received
        
        # Update position with final values
        closed_position = position._replace(
            status=PositionStatus.CLOSED,
            close_time=datetime.now(timezone.utc),
            total_realized_pnl=total_realized_pnl,
            actual_profit=actual_profit,
            spot_leg=position.spot_leg._replace(
                current_price=close_spot_price,
                realized_pnl=spot_realized_pnl,
            ),
            futures_leg=position.futures_leg._replace(
                current_price=close_futures_price,
                realized_pnl=futures_realized_pnl,
            ),
        )
        
        # Move to history
        self._position_history.append(closed_position)
        del self._positions[position_id]
        
        # Calculate position metrics
        self._calculate_position_metrics(closed_position)
        
        self._logger.logger.info(
            f"Closed arbitrage position {position_id}",
            total_realized_pnl=float(total_realized_pnl),
            funding_received=float(position.total_funding_received),
            actual_profit=float(actual_profit),
        )
        
        return closed_position
    
    def record_funding_payment(self, position_id: str, funding_amount: Decimal) -> None:
        """Record a funding payment for a position."""
        if position_id not in self._positions:
            return
        
        position = self._positions[position_id]
        updated_position = position._replace(
            total_funding_received=position.total_funding_received + funding_amount
        )
        
        self._positions[position_id] = updated_position
        
        self._logger.log_funding_payment(
            symbol=position.symbol,
            funding_rate=0.0,  # Rate not available here
            position_size=float(position.futures_leg.size),
            payment=float(funding_amount),
        )
    
    def get_active_positions(self) -> Dict[str, ArbitragePositionDetail]:
        """Get all active positions."""
        return self._positions.copy()
    
    def get_position_history(
        self,
        symbol: Optional[str] = None,
        limit: Optional[int] = None,
    ) -> List[ArbitragePositionDetail]:
        """Get position history."""
        positions = self._position_history
        
        if symbol:
            positions = [p for p in positions if p.symbol == symbol]
        
        if limit:
            positions = positions[-limit:]
        
        return positions
    
    def get_position_metrics(self, position_id: str) -> Optional[Dict]:
        """Get metrics for a specific position."""
        return self._position_metrics.get(position_id)
    
    def _update_position_with_fill(
        self,
        position: ArbitragePositionDetail,
        order_fill: OrderFilled,
    ) -> ArbitragePositionDetail:
        """Update position with order fill information."""
        instrument_id = order_fill.instrument_id
        
        if "PERP" in str(instrument_id):
            # Futures leg
            updated_futures_leg = position.futures_leg._replace(
                size=position.futures_leg.size + order_fill.last_qty,
                entry_price=order_fill.last_px,
                fill_time=order_fill.ts_event,
            )
            return position._replace(futures_leg=updated_futures_leg)
        else:
            # Spot leg
            updated_spot_leg = position.spot_leg._replace(
                size=position.spot_leg.size + order_fill.last_qty,
                entry_price=order_fill.last_px,
                fill_time=order_fill.ts_event,
            )
            return position._replace(spot_leg=updated_spot_leg)
    
    def _update_leg_price(self, leg: PositionLeg, current_price: Decimal) -> PositionLeg:
        """Update leg with current price and calculate unrealized PnL."""
        if leg.size == 0:
            return leg
        
        # Calculate unrealized PnL
        if leg.side == OrderSide.BUY:
            unrealized_pnl = (current_price - leg.entry_price) * leg.size
        else:
            unrealized_pnl = (leg.entry_price - current_price) * leg.size
        
        return leg._replace(
            current_price=current_price,
            unrealized_pnl=unrealized_pnl,
        )
    
    def _calculate_leg_realized_pnl(self, leg: PositionLeg, close_price: Decimal) -> Decimal:
        """Calculate realized PnL for a leg."""
        if leg.size == 0:
            return Decimal("0")
        
        if leg.side == OrderSide.BUY:
            return (close_price - leg.entry_price) * leg.size
        else:
            return (leg.entry_price - close_price) * leg.size
    
    def _calculate_position_metrics(self, position: ArbitragePositionDetail) -> None:
        """Calculate detailed metrics for a closed position."""
        if not position.close_time:
            return
        
        holding_period = position.close_time - position.entry_time
        
        metrics = {
            "holding_period_hours": holding_period.total_seconds() / 3600,
            "expected_profit": float(position.expected_profit),
            "actual_profit": float(position.actual_profit),
            "profit_difference": float(position.actual_profit - position.expected_profit),
            "trading_pnl": float(position.total_realized_pnl),
            "funding_pnl": float(position.total_funding_received),
            "return_on_capital": float(position.actual_profit / (
                abs(position.spot_leg.size * position.spot_leg.entry_price) +
                abs(position.futures_leg.size * position.futures_leg.entry_price)
            )) if position.spot_leg.size > 0 and position.futures_leg.size > 0 else 0.0,
            "hedge_effectiveness": self._calculate_hedge_effectiveness(position),
        }
        
        self._position_metrics[position.position_id] = metrics
    
    def _calculate_hedge_effectiveness(self, position: ArbitragePositionDetail) -> float:
        """Calculate hedge effectiveness for the position."""
        # Simple hedge effectiveness based on size ratio
        if position.spot_leg.size == 0 or position.futures_leg.size == 0:
            return 0.0
        
        size_ratio = min(
            position.spot_leg.size / position.futures_leg.size,
            position.futures_leg.size / position.spot_leg.size,
        )
        
        return float(size_ratio)

"""
Portfolio management for the funding rate arbitrage system.

This module provides portfolio management functionality for tracking positions
across spot and futures markets and calculating PnL including funding costs.
"""

from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, NamedTuple
from enum import Enum

import pandas as pd
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.position import Position
from nautilus_trader.model.objects import Money, Quantity
from nautilus_trader.portfolio.portfolio import Portfolio

from src.data.funding_rate import FundingRateData
from src.utils.calculations import calculate_funding_rate_profit
from src.utils.time_utils import get_funding_times_in_range
from src.utils.logging import TradingLogger


class PositionType(Enum):
    """Position types for arbitrage tracking."""
    SPOT_LONG = "spot_long"
    SPOT_SHORT = "spot_short"
    FUTURES_LONG = "futures_long"
    FUTURES_SHORT = "futures_short"


class ArbitragePosition(NamedTuple):
    """Arbitrage position tracking."""
    symbol: str
    arbitrage_type: str  # "long" or "short"
    spot_position_size: Decimal
    futures_position_size: Decimal
    entry_time: datetime
    entry_funding_rate: Decimal
    entry_spot_price: Decimal
    entry_futures_price: Decimal
    current_spot_price: Optional[Decimal] = None
    current_futures_price: Optional[Decimal] = None
    unrealized_pnl: Decimal = Decimal("0")
    realized_pnl: Decimal = Decimal("0")
    funding_received: Decimal = Decimal("0")


class PortfolioMetrics(NamedTuple):
    """Portfolio performance metrics."""
    total_value: Decimal
    total_pnl: Decimal
    trading_pnl: Decimal
    funding_pnl: Decimal
    unrealized_pnl: Decimal
    realized_pnl: Decimal
    total_exposure: Decimal
    leverage: Decimal
    sharpe_ratio: Decimal
    max_drawdown: Decimal
    win_rate: Decimal
    profit_factor: Decimal


class PortfolioManager:
    """
    Portfolio manager for the funding rate arbitrage system.
    """
    
    def __init__(
        self,
        portfolio: Portfolio,
        base_currency: str = "USDT",
    ) -> None:
        self._portfolio = portfolio
        self._base_currency = base_currency
        
        # Position tracking
        self._arbitrage_positions: Dict[str, ArbitragePosition] = {}
        self._closed_positions: List[ArbitragePosition] = []
        
        # Performance tracking
        self._equity_curve: List[tuple[datetime, Decimal]] = []
        self._daily_pnl: List[tuple[datetime, Decimal]] = []
        self._funding_payments: List[tuple[datetime, str, Decimal]] = []
        
        # Metrics cache
        self._last_metrics_update: Optional[datetime] = None
        self._cached_metrics: Optional[PortfolioMetrics] = None
        
        # Logger
        self._logger = TradingLogger("portfolio_manager")
    
    def open_arbitrage_position(
        self,
        symbol: str,
        arbitrage_type: str,
        spot_position_size: Decimal,
        futures_position_size: Decimal,
        entry_funding_rate: Decimal,
        entry_spot_price: Decimal,
        entry_futures_price: Decimal,
    ) -> None:
        """Open a new arbitrage position."""
        position = ArbitragePosition(
            symbol=symbol,
            arbitrage_type=arbitrage_type,
            spot_position_size=spot_position_size,
            futures_position_size=futures_position_size,
            entry_time=datetime.now(timezone.utc),
            entry_funding_rate=entry_funding_rate,
            entry_spot_price=entry_spot_price,
            entry_futures_price=entry_futures_price,
        )
        
        self._arbitrage_positions[symbol] = position
        
        self._logger.log_position_update(
            symbol=symbol,
            side=arbitrage_type,
            size=float(abs(spot_position_size) + abs(futures_position_size)),
            entry_price=float((entry_spot_price + entry_futures_price) / 2),
            current_price=float((entry_spot_price + entry_futures_price) / 2),
            pnl=0.0,
            arbitrage_type=arbitrage_type,
        )
    
    def close_arbitrage_position(
        self,
        symbol: str,
        exit_spot_price: Decimal,
        exit_futures_price: Decimal,
        realized_pnl: Decimal,
    ) -> Optional[ArbitragePosition]:
        """Close an arbitrage position."""
        if symbol not in self._arbitrage_positions:
            return None
        
        position = self._arbitrage_positions[symbol]
        
        # Update position with exit information
        closed_position = position._replace(
            current_spot_price=exit_spot_price,
            current_futures_price=exit_futures_price,
            realized_pnl=realized_pnl,
        )
        
        # Move to closed positions
        self._closed_positions.append(closed_position)
        del self._arbitrage_positions[symbol]
        
        self._logger.log_position_update(
            symbol=symbol,
            side="closed",
            size=0.0,
            entry_price=float((position.entry_spot_price + position.entry_futures_price) / 2),
            current_price=float((exit_spot_price + exit_futures_price) / 2),
            pnl=float(realized_pnl),
            arbitrage_type=position.arbitrage_type,
        )
        
        return closed_position
    
    def update_position_prices(
        self,
        symbol: str,
        current_spot_price: Decimal,
        current_futures_price: Decimal,
    ) -> None:
        """Update current prices for an arbitrage position."""
        if symbol not in self._arbitrage_positions:
            return
        
        position = self._arbitrage_positions[symbol]
        
        # Calculate unrealized PnL
        unrealized_pnl = self._calculate_unrealized_pnl(
            position, current_spot_price, current_futures_price
        )
        
        # Update position
        updated_position = position._replace(
            current_spot_price=current_spot_price,
            current_futures_price=current_futures_price,
            unrealized_pnl=unrealized_pnl,
        )
        
        self._arbitrage_positions[symbol] = updated_position
    
    def record_funding_payment(
        self,
        symbol: str,
        funding_amount: Decimal,
        funding_rate: Decimal,
    ) -> None:
        """Record a funding payment."""
        timestamp = datetime.now(timezone.utc)
        self._funding_payments.append((timestamp, symbol, funding_amount))
        
        # Update position funding received
        if symbol in self._arbitrage_positions:
            position = self._arbitrage_positions[symbol]
            updated_position = position._replace(
                funding_received=position.funding_received + funding_amount
            )
            self._arbitrage_positions[symbol] = updated_position
        
        self._logger.log_funding_payment(
            symbol=symbol,
            funding_rate=float(funding_rate),
            position_size=float(self._get_futures_position_size(symbol)),
            payment=float(funding_amount),
        )
    
    def update_equity_curve(self) -> None:
        """Update equity curve with current portfolio value."""
        timestamp = datetime.now(timezone.utc)
        total_value = self._calculate_total_portfolio_value()
        
        self._equity_curve.append((timestamp, total_value))
        
        # Calculate daily PnL if we have previous data
        if len(self._equity_curve) > 1:
            prev_value = self._equity_curve[-2][1]
            daily_pnl = total_value - prev_value
            self._daily_pnl.append((timestamp, daily_pnl))
        
        # Keep only recent data (e.g., last year)
        cutoff_date = timestamp - timedelta(days=365)
        self._equity_curve = [(t, v) for t, v in self._equity_curve if t > cutoff_date]
        self._daily_pnl = [(t, p) for t, p in self._daily_pnl if t > cutoff_date]
    
    def get_portfolio_metrics(self, force_update: bool = False) -> PortfolioMetrics:
        """
        Get comprehensive portfolio metrics.
        
        Parameters
        ----------
        force_update : bool, default False
            Force recalculation of metrics.
        
        Returns
        -------
        PortfolioMetrics
            Portfolio performance metrics.
        """
        current_time = datetime.now(timezone.utc)
        
        # Use cached metrics if recent
        if (not force_update and self._cached_metrics and self._last_metrics_update and
            (current_time - self._last_metrics_update).total_seconds() < 60):
            return self._cached_metrics
        
        # Calculate metrics
        total_value = self._calculate_total_portfolio_value()
        total_pnl = self._calculate_total_pnl()
        trading_pnl = self._calculate_trading_pnl()
        funding_pnl = self._calculate_funding_pnl()
        unrealized_pnl = self._calculate_unrealized_pnl_total()
        realized_pnl = self._calculate_realized_pnl_total()
        total_exposure = self._calculate_total_exposure()
        leverage = self._calculate_leverage()
        sharpe_ratio = self._calculate_sharpe_ratio()
        max_drawdown = self._calculate_max_drawdown()
        win_rate = self._calculate_win_rate()
        profit_factor = self._calculate_profit_factor()
        
        metrics = PortfolioMetrics(
            total_value=total_value,
            total_pnl=total_pnl,
            trading_pnl=trading_pnl,
            funding_pnl=funding_pnl,
            unrealized_pnl=unrealized_pnl,
            realized_pnl=realized_pnl,
            total_exposure=total_exposure,
            leverage=leverage,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
        )
        
        # Cache metrics
        self._cached_metrics = metrics
        self._last_metrics_update = current_time
        
        return metrics
    
    def get_active_positions(self) -> Dict[str, ArbitragePosition]:
        """Get all active arbitrage positions."""
        return self._arbitrage_positions.copy()
    
    def get_closed_positions(
        self,
        symbol: Optional[str] = None,
        limit: Optional[int] = None,
    ) -> List[ArbitragePosition]:
        """
        Get closed positions history.
        
        Parameters
        ----------
        symbol : str, optional
            Filter by symbol.
        limit : int, optional
            Limit number of results.
        
        Returns
        -------
        List[ArbitragePosition]
            Closed positions.
        """
        positions = self._closed_positions
        
        if symbol:
            positions = [p for p in positions if p.symbol == symbol]
        
        if limit:
            positions = positions[-limit:]
        
        return positions

    def _calculate_unrealized_pnl(
        self,
        position: ArbitragePosition,
        current_spot_price: Decimal,
        current_futures_price: Decimal,
    ) -> Decimal:
        """Calculate unrealized PnL for a position."""
        if position.arbitrage_type == "long":
            # Long arbitrage: long spot, short futures
            spot_pnl = (current_spot_price - position.entry_spot_price) * position.spot_position_size
            futures_pnl = (position.entry_futures_price - current_futures_price) * position.futures_position_size
        else:
            # Short arbitrage: short spot, long futures
            spot_pnl = (position.entry_spot_price - current_spot_price) * abs(position.spot_position_size)
            futures_pnl = (current_futures_price - position.entry_futures_price) * position.futures_position_size

        return spot_pnl + futures_pnl

    def _calculate_total_portfolio_value(self) -> Decimal:
        """Calculate total portfolio value."""
        total_value = Decimal("0")

        # Get balance from NautilusTrader portfolio
        for account in self._portfolio.accounts():
            balance = account.balance_total()
            if balance:
                total_value += Decimal(str(balance.as_decimal()))

        return total_value

    def _calculate_total_pnl(self) -> Decimal:
        """Calculate total PnL (realized + unrealized + funding)."""
        return (self._calculate_realized_pnl_total() +
                self._calculate_unrealized_pnl_total() +
                self._calculate_funding_pnl())

    def _calculate_trading_pnl(self) -> Decimal:
        """Calculate trading PnL (excluding funding)."""
        return self._calculate_realized_pnl_total() + self._calculate_unrealized_pnl_total()

    def _calculate_funding_pnl(self) -> Decimal:
        """Calculate total funding PnL."""
        total_funding = Decimal("0")

        # Active positions
        for position in self._arbitrage_positions.values():
            total_funding += position.funding_received

        # Closed positions
        for position in self._closed_positions:
            total_funding += position.funding_received

        return total_funding

    def _calculate_unrealized_pnl_total(self) -> Decimal:
        """Calculate total unrealized PnL."""
        total_unrealized = Decimal("0")

        for position in self._arbitrage_positions.values():
            total_unrealized += position.unrealized_pnl

        return total_unrealized

    def _calculate_realized_pnl_total(self) -> Decimal:
        """Calculate total realized PnL."""
        total_realized = Decimal("0")

        for position in self._closed_positions:
            total_realized += position.realized_pnl

        return total_realized

    def _calculate_total_exposure(self) -> Decimal:
        """Calculate total portfolio exposure."""
        total_exposure = Decimal("0")

        for position in self._arbitrage_positions.values():
            if position.current_spot_price and position.current_futures_price:
                spot_exposure = abs(position.spot_position_size) * position.current_spot_price
                futures_exposure = abs(position.futures_position_size) * position.current_futures_price
                total_exposure += spot_exposure + futures_exposure

        return total_exposure

    def _calculate_leverage(self) -> Decimal:
        """Calculate portfolio leverage."""
        total_value = self._calculate_total_portfolio_value()
        total_exposure = self._calculate_total_exposure()

        if total_value <= 0:
            return Decimal("0")

        return total_exposure / total_value

    def _calculate_sharpe_ratio(self) -> Decimal:
        """Calculate Sharpe ratio from daily PnL."""
        if len(self._daily_pnl) < 2:
            return Decimal("0")

        returns = [float(pnl) for _, pnl in self._daily_pnl]
        returns_series = pd.Series(returns)

        if returns_series.std() == 0:
            return Decimal("0")

        # Annualize (assuming daily data)
        annual_return = returns_series.mean() * 252
        annual_volatility = returns_series.std() * (252 ** 0.5)

        # Assume 2% risk-free rate
        risk_free_rate = 0.02
        sharpe = (annual_return - risk_free_rate) / annual_volatility

        return Decimal(str(sharpe))

    def _calculate_max_drawdown(self) -> Decimal:
        """Calculate maximum drawdown from equity curve."""
        if len(self._equity_curve) < 2:
            return Decimal("0")

        equity_values = [float(value) for _, value in self._equity_curve]
        equity_series = pd.Series(equity_values)

        # Calculate running maximum
        running_max = equity_series.expanding().max()

        # Calculate drawdown
        drawdown = (equity_series - running_max) / running_max

        # Return maximum drawdown as positive percentage
        max_drawdown = abs(drawdown.min())
        return Decimal(str(max_drawdown))

    def _calculate_win_rate(self) -> Decimal:
        """Calculate win rate from closed positions."""
        if not self._closed_positions:
            return Decimal("0")

        winning_trades = sum(1 for p in self._closed_positions if p.realized_pnl > 0)
        total_trades = len(self._closed_positions)

        return Decimal(str(winning_trades / total_trades))

    def _calculate_profit_factor(self) -> Decimal:
        """Calculate profit factor (gross profit / gross loss)."""
        if not self._closed_positions:
            return Decimal("0")

        gross_profit = sum(p.realized_pnl for p in self._closed_positions if p.realized_pnl > 0)
        gross_loss = abs(sum(p.realized_pnl for p in self._closed_positions if p.realized_pnl < 0))

        if gross_loss == 0:
            return Decimal("999")  # Infinite profit factor

        return gross_profit / gross_loss

    def _get_futures_position_size(self, symbol: str) -> Decimal:
        """Get futures position size for a symbol."""
        if symbol in self._arbitrage_positions:
            return self._arbitrage_positions[symbol].futures_position_size
        return Decimal("0")

    def get_portfolio_summary(self) -> Dict:
        """
        Get portfolio summary.

        Returns
        -------
        Dict
            Portfolio summary.
        """
        metrics = self.get_portfolio_metrics()

        return {
            "timestamp": datetime.now(timezone.utc),
            "total_value": float(metrics.total_value),
            "total_pnl": float(metrics.total_pnl),
            "trading_pnl": float(metrics.trading_pnl),
            "funding_pnl": float(metrics.funding_pnl),
            "unrealized_pnl": float(metrics.unrealized_pnl),
            "realized_pnl": float(metrics.realized_pnl),
            "total_exposure": float(metrics.total_exposure),
            "leverage": float(metrics.leverage),
            "sharpe_ratio": float(metrics.sharpe_ratio),
            "max_drawdown": float(metrics.max_drawdown),
            "win_rate": float(metrics.win_rate),
            "profit_factor": float(metrics.profit_factor),
            "active_positions": len(self._arbitrage_positions),
            "closed_positions": len(self._closed_positions),
            "total_funding_payments": len(self._funding_payments),
        }

"""
Alert management system for the funding rate arbitrage system.

This module provides comprehensive alerting including threshold monitoring,
anomaly detection, and notification delivery via multiple channels.
"""

import asyncio
import smtplib
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Callable, Any, NamedTuple
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from dataclasses import dataclass
import json

from src.utils.logging import TradingLogger


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertChannel(Enum):
    """Alert delivery channels."""
    EMAIL = "email"
    WEBHOOK = "webhook"
    LOG = "log"
    CONSOLE = "console"


@dataclass
class AlertRule:
    """Alert rule configuration."""
    name: str
    metric_name: str
    condition: str  # "gt", "lt", "eq", "ne", "change_pct"
    threshold: float
    severity: AlertSeverity
    channels: List[AlertChannel]
    cooldown_minutes: int = 15
    enabled: bool = True
    description: str = ""


@dataclass
class Alert:
    """Alert instance."""
    rule_name: str
    metric_name: str
    current_value: float
    threshold: float
    severity: AlertSeverity
    message: str
    timestamp: datetime
    resolved: bool = False
    resolved_timestamp: Optional[datetime] = None


class AlertManager:
    """
    Comprehensive alert management system.
    """
    
    def __init__(
        self,
        email_config: Optional[Dict[str, str]] = None,
        webhook_config: Optional[Dict[str, str]] = None,
    ) -> None:
        self._email_config = email_config or {}
        self._webhook_config = webhook_config or {}
        
        # Alert rules and state
        self._rules: Dict[str, AlertRule] = {}
        self._active_alerts: Dict[str, Alert] = {}
        self._alert_history: List[Alert] = []
        self._last_alert_times: Dict[str, datetime] = {}
        
        # Notification channels
        self._notification_callbacks: Dict[AlertChannel, List[Callable]] = {
            channel: [] for channel in AlertChannel
        }
        
        # Monitoring
        self._is_running = False
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # Logger
        self._logger = TradingLogger("alert_manager")
        
        # Setup default rules
        self._setup_default_rules()
    
    def _setup_default_rules(self) -> None:
        """Setup default alert rules."""
        default_rules = [
            AlertRule(
                name="high_drawdown",
                metric_name="trading.max_drawdown",
                condition="gt",
                threshold=0.1,  # 10%
                severity=AlertSeverity.WARNING,
                channels=[AlertChannel.EMAIL, AlertChannel.LOG],
                description="Maximum drawdown exceeded 10%",
            ),
            AlertRule(
                name="critical_drawdown",
                metric_name="trading.max_drawdown",
                condition="gt",
                threshold=0.2,  # 20%
                severity=AlertSeverity.CRITICAL,
                channels=[AlertChannel.EMAIL, AlertChannel.WEBHOOK],
                description="CRITICAL: Maximum drawdown exceeded 20%",
            ),
            AlertRule(
                name="low_win_rate",
                metric_name="trading.win_rate",
                condition="lt",
                threshold=0.3,  # 30%
                severity=AlertSeverity.WARNING,
                channels=[AlertChannel.EMAIL, AlertChannel.LOG],
                description="Win rate dropped below 30%",
            ),
            AlertRule(
                name="high_system_cpu",
                metric_name="system.cpu_usage_percent",
                condition="gt",
                threshold=80.0,
                severity=AlertSeverity.WARNING,
                channels=[AlertChannel.LOG],
                description="High CPU usage detected",
            ),
            AlertRule(
                name="high_system_memory",
                metric_name="system.memory_usage_percent",
                condition="gt",
                threshold=85.0,
                severity=AlertSeverity.WARNING,
                channels=[AlertChannel.LOG],
                description="High memory usage detected",
            ),
            AlertRule(
                name="api_latency_high",
                metric_name="system.api_response_time_ms",
                condition="gt",
                threshold=5000.0,  # 5 seconds
                severity=AlertSeverity.ERROR,
                channels=[AlertChannel.EMAIL, AlertChannel.LOG],
                description="API response time is too high",
            ),
            AlertRule(
                name="data_feed_stale",
                metric_name="system.data_feed_latency_ms",
                condition="gt",
                threshold=30000.0,  # 30 seconds
                severity=AlertSeverity.CRITICAL,
                channels=[AlertChannel.EMAIL, AlertChannel.WEBHOOK],
                description="Data feed is stale",
            ),
            AlertRule(
                name="execution_rate_low",
                metric_name="arbitrage.execution_rate",
                condition="lt",
                threshold=0.5,  # 50%
                severity=AlertSeverity.WARNING,
                channels=[AlertChannel.EMAIL, AlertChannel.LOG],
                description="Arbitrage execution rate is low",
            ),
        ]
        
        for rule in default_rules:
            self._rules[rule.name] = rule
    
    def add_rule(self, rule: AlertRule) -> None:
        """Add an alert rule."""
        self._rules[rule.name] = rule
        self._logger.logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str) -> None:
        """Remove an alert rule."""
        if rule_name in self._rules:
            del self._rules[rule_name]
            self._logger.logger.info(f"Removed alert rule: {rule_name}")
    
    def enable_rule(self, rule_name: str) -> None:
        """Enable an alert rule."""
        if rule_name in self._rules:
            self._rules[rule_name].enabled = True
            self._logger.logger.info(f"Enabled alert rule: {rule_name}")
    
    def disable_rule(self, rule_name: str) -> None:
        """Disable an alert rule."""
        if rule_name in self._rules:
            self._rules[rule_name].enabled = False
            self._logger.logger.info(f"Disabled alert rule: {rule_name}")
    
    def add_notification_callback(
        self,
        channel: AlertChannel,
        callback: Callable[[Alert], None],
    ) -> None:
        """Add notification callback for a channel."""
        self._notification_callbacks[channel].append(callback)
    
    async def start(self) -> None:
        """Start the alert manager."""
        if self._is_running:
            return
        
        self._logger.logger.info("Starting alert manager")
        
        self._is_running = True
        self._monitoring_task = asyncio.create_task(self._monitor_alerts())
        
        self._logger.logger.info(f"Alert manager started with {len(self._rules)} rules")
    
    async def stop(self) -> None:
        """Stop the alert manager."""
        if not self._is_running:
            return
        
        self._logger.logger.info("Stopping alert manager")
        
        self._is_running = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        self._logger.logger.info("Alert manager stopped")
    
    def check_metric(self, metric_name: str, value: float) -> None:
        """Check a metric value against alert rules."""
        current_time = datetime.now(timezone.utc)
        
        for rule in self._rules.values():
            if not rule.enabled or rule.metric_name != metric_name:
                continue
            
            # Check cooldown
            last_alert_time = self._last_alert_times.get(rule.name)
            if (last_alert_time and 
                (current_time - last_alert_time).total_seconds() < rule.cooldown_minutes * 60):
                continue
            
            # Evaluate condition
            triggered = self._evaluate_condition(rule, value)
            
            if triggered:
                self._trigger_alert(rule, value, current_time)
    
    def _evaluate_condition(self, rule: AlertRule, value: float) -> bool:
        """Evaluate if a rule condition is met."""
        if rule.condition == "gt":
            return value > rule.threshold
        elif rule.condition == "lt":
            return value < rule.threshold
        elif rule.condition == "eq":
            return abs(value - rule.threshold) < 1e-6
        elif rule.condition == "ne":
            return abs(value - rule.threshold) >= 1e-6
        elif rule.condition == "change_pct":
            # For percentage change conditions, would need historical data
            return False
        else:
            return False
    
    def _trigger_alert(self, rule: AlertRule, value: float, timestamp: datetime) -> None:
        """Trigger an alert."""
        message = f"{rule.description}: {rule.metric_name} = {value:.4f} (threshold: {rule.threshold:.4f})"
        
        alert = Alert(
            rule_name=rule.name,
            metric_name=rule.metric_name,
            current_value=value,
            threshold=rule.threshold,
            severity=rule.severity,
            message=message,
            timestamp=timestamp,
        )
        
        # Store alert
        self._active_alerts[rule.name] = alert
        self._alert_history.append(alert)
        self._last_alert_times[rule.name] = timestamp
        
        # Send notifications
        asyncio.create_task(self._send_notifications(alert, rule.channels))
        
        self._logger.logger.warning(f"Alert triggered: {rule.name} - {message}")
    
    async def _send_notifications(self, alert: Alert, channels: List[AlertChannel]) -> None:
        """Send alert notifications through specified channels."""
        for channel in channels:
            try:
                if channel == AlertChannel.EMAIL:
                    await self._send_email_notification(alert)
                elif channel == AlertChannel.WEBHOOK:
                    await self._send_webhook_notification(alert)
                elif channel == AlertChannel.LOG:
                    self._send_log_notification(alert)
                elif channel == AlertChannel.CONSOLE:
                    self._send_console_notification(alert)
                
                # Call custom callbacks
                for callback in self._notification_callbacks[channel]:
                    try:
                        callback(alert)
                    except Exception as e:
                        self._logger.logger.error(f"Error in notification callback: {e}")
                        
            except Exception as e:
                self._logger.logger.error(f"Error sending {channel.value} notification: {e}")
    
    async def _send_email_notification(self, alert: Alert) -> None:
        """Send email notification."""
        if not self._email_config:
            return
        
        try:
            smtp_server = self._email_config.get("smtp_server")
            smtp_port = int(self._email_config.get("smtp_port", 587))
            username = self._email_config.get("username")
            password = self._email_config.get("password")
            from_email = self._email_config.get("from_email")
            to_emails = self._email_config.get("to_emails", "").split(",")
            
            if not all([smtp_server, username, password, from_email, to_emails]):
                self._logger.logger.warning("Incomplete email configuration")
                return
            
            # Create message
            msg = MIMEMultipart()
            msg["From"] = from_email
            msg["To"] = ", ".join(to_emails)
            msg["Subject"] = f"[{alert.severity.value.upper()}] Funding Rate Arbitrage Alert"
            
            body = f"""
Alert Details:
- Rule: {alert.rule_name}
- Metric: {alert.metric_name}
- Current Value: {alert.current_value:.4f}
- Threshold: {alert.threshold:.4f}
- Severity: {alert.severity.value.upper()}
- Time: {alert.timestamp}
- Message: {alert.message}

This is an automated alert from the Funding Rate Arbitrage System.
"""
            
            msg.attach(MIMEText(body, "plain"))
            
            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(username, password)
            server.send_message(msg)
            server.quit()
            
            self._logger.logger.info(f"Email alert sent for {alert.rule_name}")
            
        except Exception as e:
            self._logger.logger.error(f"Failed to send email alert: {e}")
    
    async def _send_webhook_notification(self, alert: Alert) -> None:
        """Send webhook notification."""
        if not self._webhook_config:
            return
        
        try:
            import aiohttp
            
            webhook_url = self._webhook_config.get("url")
            if not webhook_url:
                return
            
            payload = {
                "alert": {
                    "rule_name": alert.rule_name,
                    "metric_name": alert.metric_name,
                    "current_value": alert.current_value,
                    "threshold": alert.threshold,
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat(),
                }
            }
            
            headers = {"Content-Type": "application/json"}
            
            # Add custom headers if configured
            custom_headers = self._webhook_config.get("headers", {})
            headers.update(custom_headers)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10),
                ) as response:
                    if response.status == 200:
                        self._logger.logger.info(f"Webhook alert sent for {alert.rule_name}")
                    else:
                        self._logger.logger.error(f"Webhook alert failed: {response.status}")
                        
        except Exception as e:
            self._logger.logger.error(f"Failed to send webhook alert: {e}")
    
    def _send_log_notification(self, alert: Alert) -> None:
        """Send log notification."""
        log_level = {
            AlertSeverity.INFO: "info",
            AlertSeverity.WARNING: "warning",
            AlertSeverity.ERROR: "error",
            AlertSeverity.CRITICAL: "critical",
        }[alert.severity]
        
        getattr(self._logger.logger, log_level)(
            f"ALERT: {alert.message}",
            rule=alert.rule_name,
            metric=alert.metric_name,
            value=alert.current_value,
            threshold=alert.threshold,
        )
    
    def _send_console_notification(self, alert: Alert) -> None:
        """Send console notification."""
        severity_colors = {
            AlertSeverity.INFO: "\033[94m",      # Blue
            AlertSeverity.WARNING: "\033[93m",   # Yellow
            AlertSeverity.ERROR: "\033[91m",     # Red
            AlertSeverity.CRITICAL: "\033[95m",  # Magenta
        }
        
        color = severity_colors.get(alert.severity, "")
        reset = "\033[0m"
        
        print(f"{color}[{alert.severity.value.upper()}] {alert.message}{reset}")
    
    def resolve_alert(self, rule_name: str) -> None:
        """Manually resolve an alert."""
        if rule_name in self._active_alerts:
            alert = self._active_alerts[rule_name]
            alert.resolved = True
            alert.resolved_timestamp = datetime.now(timezone.utc)
            del self._active_alerts[rule_name]
            
            self._logger.logger.info(f"Alert resolved: {rule_name}")
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts."""
        return list(self._active_alerts.values())
    
    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """Get alert history for a time period."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        return [
            alert for alert in self._alert_history
            if alert.timestamp > cutoff_time
        ]
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get alert summary statistics."""
        active_alerts = self.get_active_alerts()
        recent_alerts = self.get_alert_history(24)
        
        severity_counts = {severity.value: 0 for severity in AlertSeverity}
        for alert in recent_alerts:
            severity_counts[alert.severity.value] += 1
        
        return {
            "active_alerts": len(active_alerts),
            "recent_alerts_24h": len(recent_alerts),
            "severity_breakdown": severity_counts,
            "rules_configured": len(self._rules),
            "rules_enabled": len([r for r in self._rules.values() if r.enabled]),
        }
    
    async def _monitor_alerts(self) -> None:
        """Monitor for alert auto-resolution."""
        while self._is_running:
            try:
                # Auto-resolve alerts that are no longer triggered
                # This would require re-checking metric values
                
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.logger.error(f"Error in alert monitoring: {e}")
                await asyncio.sleep(60)

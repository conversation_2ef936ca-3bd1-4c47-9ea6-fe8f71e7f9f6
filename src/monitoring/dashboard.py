"""
Monitoring dashboard for the funding rate arbitrage system.

This module provides a web-based dashboard for real-time monitoring of
strategy performance, system health, and arbitrage opportunities.
"""

import asyncio
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

import pandas as pd
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from starlette.requests import Request

from src.monitoring.metrics import MetricsCollector, TradingMetrics, SystemMetrics, ArbitrageMetrics
from src.monitoring.alerts import <PERSON><PERSON><PERSON><PERSON><PERSON>, Alert
from src.monitoring.analytics import PerformanceAnalyzer, PerformanceReport
from src.utils.logging import TradingLogger


class DashboardData:
    """Container for dashboard data."""
    
    def __init__(self) -> None:
        self.trading_metrics = TradingMetrics()
        self.system_metrics = SystemMetrics()
        self.arbitrage_metrics = ArbitrageMetrics()
        self.active_alerts: List[Alert] = []
        self.performance_report: Optional[PerformanceReport] = None
        self.equity_curve: List[Dict] = []
        self.recent_trades: List[Dict] = []
        self.funding_rates: Dict[str, float] = {}
        self.spreads: Dict[str, float] = {}
        self.last_update = datetime.now(timezone.utc)


class MonitoringDashboard:
    """
    Web-based monitoring dashboard.
    """
    
    def __init__(
        self,
        metrics_collector: MetricsCollector,
        alert_manager: AlertManager,
        performance_analyzer: PerformanceAnalyzer,
        host: str = "localhost",
        port: int = 8080,
    ) -> None:
        self._metrics_collector = metrics_collector
        self._alert_manager = alert_manager
        self._performance_analyzer = performance_analyzer
        self._host = host
        self._port = port
        
        # Dashboard data
        self._dashboard_data = DashboardData()
        
        # WebSocket connections
        self._websocket_connections: List[WebSocket] = []
        
        # FastAPI app
        self._app = FastAPI(title="Funding Rate Arbitrage Dashboard")
        self._setup_routes()
        
        # Update tasks
        self._update_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # Logger
        self._logger = TradingLogger("monitoring_dashboard")
    
    def _setup_routes(self) -> None:
        """Setup FastAPI routes."""
        
        @self._app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """Main dashboard page."""
            return self._render_dashboard_html()
        
        @self._app.get("/api/status")
        async def get_status():
            """Get system status."""
            return {
                "status": "running" if self._is_running else "stopped",
                "timestamp": datetime.now(timezone.utc),
                "uptime_hours": (datetime.now(timezone.utc) - self._dashboard_data.last_update).total_seconds() / 3600,
            }
        
        @self._app.get("/api/metrics")
        async def get_metrics():
            """Get current metrics."""
            return {
                "trading": self._dashboard_data.trading_metrics.__dict__,
                "system": self._dashboard_data.system_metrics.__dict__,
                "arbitrage": self._dashboard_data.arbitrage_metrics.__dict__,
                "timestamp": self._dashboard_data.last_update,
            }
        
        @self._app.get("/api/alerts")
        async def get_alerts():
            """Get active alerts."""
            return {
                "active_alerts": [
                    {
                        "rule_name": alert.rule_name,
                        "message": alert.message,
                        "severity": alert.severity.value,
                        "timestamp": alert.timestamp,
                    }
                    for alert in self._dashboard_data.active_alerts
                ],
                "alert_summary": self._alert_manager.get_alert_summary(),
            }
        
        @self._app.get("/api/performance")
        async def get_performance():
            """Get performance report."""
            if self._dashboard_data.performance_report:
                return {
                    "total_return": float(self._dashboard_data.performance_report.total_return),
                    "annualized_return": float(self._dashboard_data.performance_report.annualized_return),
                    "sharpe_ratio": float(self._dashboard_data.performance_report.sharpe_ratio),
                    "max_drawdown": float(self._dashboard_data.performance_report.max_drawdown),
                    "win_rate": float(self._dashboard_data.performance_report.win_rate),
                    "profit_factor": float(self._dashboard_data.performance_report.profit_factor),
                    "total_trades": self._dashboard_data.performance_report.total_trades,
                }
            return {}
        
        @self._app.get("/api/equity_curve")
        async def get_equity_curve():
            """Get equity curve data."""
            return {"equity_curve": self._dashboard_data.equity_curve}
        
        @self._app.get("/api/trades")
        async def get_recent_trades():
            """Get recent trades."""
            return {"trades": self._dashboard_data.recent_trades}
        
        @self._app.get("/api/funding_rates")
        async def get_funding_rates():
            """Get current funding rates."""
            return {"funding_rates": self._dashboard_data.funding_rates}
        
        @self._app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates."""
            await websocket.accept()
            self._websocket_connections.append(websocket)
            
            try:
                while True:
                    # Send periodic updates
                    await self._send_websocket_update(websocket)
                    await asyncio.sleep(5)  # Update every 5 seconds
                    
            except WebSocketDisconnect:
                self._websocket_connections.remove(websocket)
    
    async def start(self) -> None:
        """Start the monitoring dashboard."""
        if self._is_running:
            return
        
        self._logger.logger.info(f"Starting monitoring dashboard on {self._host}:{self._port}")
        
        # Start update task
        self._update_task = asyncio.create_task(self._update_dashboard_data())
        self._is_running = True
        
        # Start FastAPI server
        import uvicorn
        config = uvicorn.Config(
            app=self._app,
            host=self._host,
            port=self._port,
            log_level="info",
        )
        server = uvicorn.Server(config)
        
        # Run server in background
        asyncio.create_task(server.serve())
        
        self._logger.logger.info("Monitoring dashboard started")
    
    async def stop(self) -> None:
        """Stop the monitoring dashboard."""
        if not self._is_running:
            return
        
        self._logger.logger.info("Stopping monitoring dashboard")
        
        self._is_running = False
        
        if self._update_task:
            self._update_task.cancel()
            try:
                await self._update_task
            except asyncio.CancelledError:
                pass
        
        # Close WebSocket connections
        for websocket in self._websocket_connections:
            await websocket.close()
        
        self._logger.logger.info("Monitoring dashboard stopped")
    
    async def _update_dashboard_data(self) -> None:
        """Update dashboard data periodically."""
        while self._is_running:
            try:
                # Update metrics
                current_metrics = self._metrics_collector.get_current_metrics()
                self._dashboard_data.trading_metrics = current_metrics["trading"]
                self._dashboard_data.system_metrics = current_metrics["system"]
                self._dashboard_data.arbitrage_metrics = current_metrics["arbitrage"]
                
                # Update alerts
                self._dashboard_data.active_alerts = self._alert_manager.get_active_alerts()
                
                # Update equity curve (last 100 points)
                equity_history = self._metrics_collector.get_metric_history("portfolio.equity", hours=24)
                self._dashboard_data.equity_curve = [
                    {
                        "timestamp": metric.timestamp.isoformat(),
                        "value": metric.value,
                    }
                    for metric in equity_history[-100:]
                ]
                
                # Update recent trades (last 20)
                trade_history = self._metrics_collector.get_metric_history("trade.pnl", hours=24)
                self._dashboard_data.recent_trades = [
                    {
                        "timestamp": metric.timestamp.isoformat(),
                        "pnl": metric.value,
                        "symbol": metric.tags.get("symbol", ""),
                        "type": metric.tags.get("type", ""),
                    }
                    for metric in trade_history[-20:]
                ]
                
                # Update funding rates
                funding_history = self._metrics_collector.get_metric_history("arbitrage.funding_rate", hours=1)
                funding_by_symbol = {}
                for metric in funding_history:
                    symbol = metric.tags.get("symbol", "")
                    if symbol:
                        funding_by_symbol[symbol] = metric.value
                self._dashboard_data.funding_rates = funding_by_symbol
                
                # Update spreads
                spread_history = self._metrics_collector.get_metric_history("arbitrage.spread", hours=1)
                spreads_by_symbol = {}
                for metric in spread_history:
                    symbol = metric.tags.get("symbol", "")
                    if symbol:
                        spreads_by_symbol[symbol] = metric.value
                self._dashboard_data.spreads = spreads_by_symbol
                
                self._dashboard_data.last_update = datetime.now(timezone.utc)
                
                # Send WebSocket updates
                await self._broadcast_websocket_updates()
                
                # Wait before next update
                await asyncio.sleep(10)  # Update every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.logger.error(f"Error updating dashboard data: {e}")
                await asyncio.sleep(10)
    
    async def _send_websocket_update(self, websocket: WebSocket) -> None:
        """Send update to a specific WebSocket connection."""
        try:
            update_data = {
                "type": "update",
                "data": {
                    "metrics": {
                        "trading": self._dashboard_data.trading_metrics.__dict__,
                        "system": self._dashboard_data.system_metrics.__dict__,
                        "arbitrage": self._dashboard_data.arbitrage_metrics.__dict__,
                    },
                    "alerts": len(self._dashboard_data.active_alerts),
                    "funding_rates": self._dashboard_data.funding_rates,
                    "spreads": self._dashboard_data.spreads,
                    "timestamp": self._dashboard_data.last_update.isoformat(),
                }
            }
            
            await websocket.send_text(json.dumps(update_data))
            
        except Exception as e:
            self._logger.logger.error(f"Error sending WebSocket update: {e}")
    
    async def _broadcast_websocket_updates(self) -> None:
        """Broadcast updates to all WebSocket connections."""
        if not self._websocket_connections:
            return
        
        # Remove disconnected connections
        active_connections = []
        for websocket in self._websocket_connections:
            try:
                await self._send_websocket_update(websocket)
                active_connections.append(websocket)
            except:
                # Connection is dead, remove it
                pass
        
        self._websocket_connections = active_connections
    
    def _render_dashboard_html(self) -> str:
        """Render the main dashboard HTML."""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Funding Rate Arbitrage Dashboard</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; }
                .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
                .metric-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .metric-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #2c3e50; }
                .metric-value { font-size: 24px; font-weight: bold; color: #27ae60; }
                .metric-label { font-size: 14px; color: #7f8c8d; margin-top: 5px; }
                .alert { background: #e74c3c; color: white; padding: 10px; border-radius: 4px; margin: 5px 0; }
                .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
                .status-running { background-color: #27ae60; }
                .status-stopped { background-color: #e74c3c; }
                #charts { margin-top: 20px; }
                .chart-container { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Funding Rate Arbitrage Dashboard</h1>
                    <p><span id="status-indicator" class="status-indicator status-running"></span>System Status: <span id="system-status">Running</span></p>
                    <p>Last Update: <span id="last-update">-</span></p>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-title">Trading Performance</div>
                        <div class="metric-value" id="total-pnl">$0.00</div>
                        <div class="metric-label">Total PnL</div>
                        <div style="margin-top: 10px;">
                            <div>Win Rate: <span id="win-rate">0%</span></div>
                            <div>Total Trades: <span id="total-trades">0</span></div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-title">Risk Metrics</div>
                        <div class="metric-value" id="sharpe-ratio">0.00</div>
                        <div class="metric-label">Sharpe Ratio</div>
                        <div style="margin-top: 10px;">
                            <div>Max Drawdown: <span id="max-drawdown">0%</span></div>
                            <div>Profit Factor: <span id="profit-factor">0.00</span></div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-title">System Health</div>
                        <div class="metric-value" id="cpu-usage">0%</div>
                        <div class="metric-label">CPU Usage</div>
                        <div style="margin-top: 10px;">
                            <div>Memory: <span id="memory-usage">0%</span></div>
                            <div>API Latency: <span id="api-latency">0ms</span></div>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-title">Arbitrage</div>
                        <div class="metric-value" id="execution-rate">0%</div>
                        <div class="metric-label">Execution Rate</div>
                        <div style="margin-top: 10px;">
                            <div>Opportunities: <span id="opportunities">0</span></div>
                            <div>Avg Funding: <span id="avg-funding">0%</span></div>
                        </div>
                    </div>
                </div>
                
                <div id="alerts-section" style="display: none;">
                    <div class="metric-card">
                        <div class="metric-title">Active Alerts</div>
                        <div id="alerts-container"></div>
                    </div>
                </div>
                
                <div id="charts">
                    <div class="chart-container">
                        <h3>Funding Rates</h3>
                        <div id="funding-rates-display"></div>
                    </div>
                </div>
            </div>
            
            <script>
                // WebSocket connection for real-time updates
                const ws = new WebSocket(`ws://${window.location.host}/ws`);
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    if (data.type === 'update') {
                        updateDashboard(data.data);
                    }
                };
                
                function updateDashboard(data) {
                    // Update metrics
                    const trading = data.metrics.trading;
                    const system = data.metrics.system;
                    const arbitrage = data.metrics.arbitrage;
                    
                    document.getElementById('total-pnl').textContent = `$${trading.total_pnl.toFixed(2)}`;
                    document.getElementById('win-rate').textContent = `${(trading.win_rate * 100).toFixed(1)}%`;
                    document.getElementById('total-trades').textContent = trading.total_trades;
                    document.getElementById('sharpe-ratio').textContent = trading.sharpe_ratio.toFixed(2);
                    document.getElementById('max-drawdown').textContent = `${(trading.max_drawdown * 100).toFixed(1)}%`;
                    document.getElementById('profit-factor').textContent = trading.profit_factor.toFixed(2);
                    
                    document.getElementById('cpu-usage').textContent = `${system.cpu_usage_percent.toFixed(1)}%`;
                    document.getElementById('memory-usage').textContent = `${system.memory_usage_percent.toFixed(1)}%`;
                    document.getElementById('api-latency').textContent = `${system.api_response_time_ms.toFixed(0)}ms`;
                    
                    document.getElementById('execution-rate').textContent = `${(arbitrage.execution_rate * 100).toFixed(1)}%`;
                    document.getElementById('opportunities').textContent = arbitrage.opportunities_detected;
                    document.getElementById('avg-funding').textContent = `${(arbitrage.average_funding_rate * 100).toFixed(3)}%`;
                    
                    // Update funding rates
                    const fundingRatesDiv = document.getElementById('funding-rates-display');
                    let fundingHtml = '';
                    for (const [symbol, rate] of Object.entries(data.funding_rates)) {
                        fundingHtml += `<div>${symbol}: ${(rate * 100).toFixed(4)}%</div>`;
                    }
                    fundingRatesDiv.innerHTML = fundingHtml || 'No funding rate data';
                    
                    // Update alerts
                    if (data.alerts > 0) {
                        document.getElementById('alerts-section').style.display = 'block';
                    } else {
                        document.getElementById('alerts-section').style.display = 'none';
                    }
                    
                    // Update timestamp
                    document.getElementById('last-update').textContent = new Date(data.timestamp).toLocaleString();
                }
                
                // Initial data load
                fetch('/api/metrics')
                    .then(response => response.json())
                    .then(data => updateDashboard({metrics: data}));
            </script>
        </body>
        </html>
        """
        return html
    
    def get_dashboard_url(self) -> str:
        """Get the dashboard URL."""
        return f"http://{self._host}:{self._port}"

"""
Metrics collection system for the funding rate arbitrage system.

This module provides comprehensive metrics collection including trading performance,
system health, and arbitrage opportunity tracking.
"""

import asyncio
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, NamedTuple
from collections import deque, defaultdict
from dataclasses import dataclass, field
from enum import Enum

import pandas as pd
import numpy as np

from src.utils.logging import TradingLogger


class MetricType(Enum):
    """Metric type enumeration."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class Metric:
    """Individual metric data point."""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    metric_type: MetricType = MetricType.GAUGE


@dataclass
class TradingMetrics:
    """Trading performance metrics."""
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: Decimal = Decimal("0")
    unrealized_pnl: Decimal = Decimal("0")
    realized_pnl: Decimal = Decimal("0")
    funding_pnl: Decimal = Decimal("0")
    trading_pnl: Decimal = Decimal("0")
    total_fees: Decimal = Decimal("0")
    win_rate: float = 0.0
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    average_trade_duration_hours: float = 0.0
    trades_per_day: float = 0.0


@dataclass
class SystemMetrics:
    """System health metrics."""
    cpu_usage_percent: float = 0.0
    memory_usage_percent: float = 0.0
    disk_usage_percent: float = 0.0
    network_latency_ms: float = 0.0
    api_response_time_ms: float = 0.0
    data_feed_latency_ms: float = 0.0
    order_execution_time_ms: float = 0.0
    uptime_hours: float = 0.0
    error_rate_percent: float = 0.0
    active_connections: int = 0


@dataclass
class ArbitrageMetrics:
    """Arbitrage opportunity metrics."""
    opportunities_detected: int = 0
    opportunities_executed: int = 0
    execution_rate: float = 0.0
    average_funding_rate: float = 0.0
    funding_rate_volatility: float = 0.0
    average_spread: float = 0.0
    spread_volatility: float = 0.0
    time_to_execution_ms: float = 0.0
    missed_opportunities: int = 0
    rejected_opportunities: int = 0


class MetricsCollector:
    """
    Comprehensive metrics collection system.
    """
    
    def __init__(
        self,
        collection_interval_seconds: int = 60,
        max_history_points: int = 10000,
        enable_system_metrics: bool = True,
    ) -> None:
        self._collection_interval = collection_interval_seconds
        self._max_history = max_history_points
        self._enable_system_metrics = enable_system_metrics
        
        # Metrics storage
        self._metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history_points))
        self._current_metrics: Dict[str, Metric] = {}
        
        # Aggregated metrics
        self._trading_metrics = TradingMetrics()
        self._system_metrics = SystemMetrics()
        self._arbitrage_metrics = ArbitrageMetrics()
        
        # Collection state
        self._is_running = False
        self._collection_task: Optional[asyncio.Task] = None
        self._start_time = datetime.now(timezone.utc)
        
        # Callbacks
        self._metric_callbacks: List[callable] = []
        
        # Logger
        self._logger = TradingLogger("metrics_collector")
    
    def add_metric_callback(self, callback: callable) -> None:
        """Add callback for metric updates."""
        self._metric_callbacks.append(callback)
    
    async def start(self) -> None:
        """Start metrics collection."""
        if self._is_running:
            return
        
        self._logger.logger.info("Starting metrics collection")
        
        self._is_running = True
        self._start_time = datetime.now(timezone.utc)
        
        # Start collection task
        self._collection_task = asyncio.create_task(self._collect_metrics_loop())
        
        self._logger.logger.info(
            f"Metrics collection started with {self._collection_interval}s interval"
        )
    
    async def stop(self) -> None:
        """Stop metrics collection."""
        if not self._is_running:
            return
        
        self._logger.logger.info("Stopping metrics collection")
        
        self._is_running = False
        
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        
        self._logger.logger.info("Metrics collection stopped")
    
    def record_metric(
        self,
        name: str,
        value: float,
        tags: Optional[Dict[str, str]] = None,
        metric_type: MetricType = MetricType.GAUGE,
    ) -> None:
        """Record a metric value."""
        timestamp = datetime.now(timezone.utc)
        
        metric = Metric(
            name=name,
            value=value,
            timestamp=timestamp,
            tags=tags or {},
            metric_type=metric_type,
        )
        
        # Store current metric
        self._current_metrics[name] = metric
        
        # Add to history
        self._metrics_history[name].append(metric)
        
        # Trigger callbacks
        for callback in self._metric_callbacks:
            try:
                callback(metric)
            except Exception as e:
                self._logger.logger.error(f"Error in metric callback: {e}")
    
    def record_trade_executed(
        self,
        symbol: str,
        pnl: Decimal,
        fees: Decimal,
        duration_hours: float,
        arbitrage_type: str,
    ) -> None:
        """Record a completed trade."""
        self._trading_metrics.total_trades += 1
        
        if pnl > 0:
            self._trading_metrics.winning_trades += 1
        else:
            self._trading_metrics.losing_trades += 1
        
        self._trading_metrics.total_pnl += pnl
        self._trading_metrics.total_fees += fees
        
        # Update derived metrics
        self._update_trading_metrics()
        
        # Record individual metrics
        self.record_metric("trade.pnl", float(pnl), {"symbol": symbol, "type": arbitrage_type})
        self.record_metric("trade.fees", float(fees), {"symbol": symbol})
        self.record_metric("trade.duration_hours", duration_hours, {"symbol": symbol})
        self.record_metric("trades.total", self._trading_metrics.total_trades, metric_type=MetricType.COUNTER)
    
    def record_arbitrage_opportunity(
        self,
        symbol: str,
        funding_rate: float,
        spread: float,
        executed: bool,
        execution_time_ms: Optional[float] = None,
    ) -> None:
        """Record an arbitrage opportunity."""
        self._arbitrage_metrics.opportunities_detected += 1
        
        if executed:
            self._arbitrage_metrics.opportunities_executed += 1
            if execution_time_ms:
                self._arbitrage_metrics.time_to_execution_ms = execution_time_ms
        
        # Update derived metrics
        self._update_arbitrage_metrics()
        
        # Record individual metrics
        self.record_metric("arbitrage.funding_rate", funding_rate, {"symbol": symbol})
        self.record_metric("arbitrage.spread", spread, {"symbol": symbol})
        self.record_metric("arbitrage.opportunities_detected", self._arbitrage_metrics.opportunities_detected, metric_type=MetricType.COUNTER)
        
        if executed:
            self.record_metric("arbitrage.opportunities_executed", self._arbitrage_metrics.opportunities_executed, metric_type=MetricType.COUNTER)
            if execution_time_ms:
                self.record_metric("arbitrage.execution_time_ms", execution_time_ms, {"symbol": symbol})
    
    def record_system_metric(self, name: str, value: float) -> None:
        """Record a system metric."""
        self.record_metric(f"system.{name}", value)
        
        # Update system metrics object
        if hasattr(self._system_metrics, name):
            setattr(self._system_metrics, name, value)
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current metrics snapshot."""
        return {
            "timestamp": datetime.now(timezone.utc),
            "trading": self._trading_metrics,
            "system": self._system_metrics,
            "arbitrage": self._arbitrage_metrics,
            "uptime_hours": (datetime.now(timezone.utc) - self._start_time).total_seconds() / 3600,
        }
    
    def get_metric_history(
        self,
        metric_name: str,
        hours: int = 24,
    ) -> List[Metric]:
        """Get metric history for a specific metric."""
        if metric_name not in self._metrics_history:
            return []
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        return [
            metric for metric in self._metrics_history[metric_name]
            if metric.timestamp > cutoff_time
        ]
    
    def get_metrics_dataframe(
        self,
        metric_names: Optional[List[str]] = None,
        hours: int = 24,
    ) -> pd.DataFrame:
        """Get metrics as pandas DataFrame."""
        data = []
        
        metric_names = metric_names or list(self._metrics_history.keys())
        
        for metric_name in metric_names:
            history = self.get_metric_history(metric_name, hours)
            for metric in history:
                data.append({
                    "metric_name": metric.name,
                    "value": metric.value,
                    "timestamp": metric.timestamp,
                    "tags": str(metric.tags),
                })
        
        return pd.DataFrame(data)
    
    def calculate_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Calculate performance summary over a time period."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        # Get PnL history
        pnl_history = [
            metric.value for metric in self.get_metric_history("trade.pnl", hours)
        ]
        
        # Get trade count
        trade_count = len(pnl_history)
        
        # Calculate summary statistics
        summary = {
            "period_hours": hours,
            "total_trades": trade_count,
            "total_pnl": sum(pnl_history) if pnl_history else 0,
            "average_pnl_per_trade": np.mean(pnl_history) if pnl_history else 0,
            "pnl_std": np.std(pnl_history) if len(pnl_history) > 1 else 0,
            "win_rate": len([p for p in pnl_history if p > 0]) / max(1, trade_count),
            "profit_factor": self._calculate_profit_factor(pnl_history),
            "max_win": max(pnl_history) if pnl_history else 0,
            "max_loss": min(pnl_history) if pnl_history else 0,
        }
        
        return summary
    
    def _update_trading_metrics(self) -> None:
        """Update derived trading metrics."""
        total_trades = self._trading_metrics.total_trades
        
        if total_trades > 0:
            self._trading_metrics.win_rate = self._trading_metrics.winning_trades / total_trades
        
        # Calculate profit factor
        winning_pnl = sum(
            metric.value for metric in self._metrics_history["trade.pnl"]
            if metric.value > 0
        )
        losing_pnl = abs(sum(
            metric.value for metric in self._metrics_history["trade.pnl"]
            if metric.value < 0
        ))
        
        if losing_pnl > 0:
            self._trading_metrics.profit_factor = winning_pnl / losing_pnl
        else:
            self._trading_metrics.profit_factor = float('inf') if winning_pnl > 0 else 0
    
    def _update_arbitrage_metrics(self) -> None:
        """Update derived arbitrage metrics."""
        detected = self._arbitrage_metrics.opportunities_detected
        executed = self._arbitrage_metrics.opportunities_executed
        
        if detected > 0:
            self._arbitrage_metrics.execution_rate = executed / detected
        
        # Calculate average funding rate
        funding_rates = [
            metric.value for metric in self._metrics_history["arbitrage.funding_rate"]
        ]
        if funding_rates:
            self._arbitrage_metrics.average_funding_rate = np.mean(funding_rates)
            self._arbitrage_metrics.funding_rate_volatility = np.std(funding_rates)
        
        # Calculate average spread
        spreads = [
            metric.value for metric in self._metrics_history["arbitrage.spread"]
        ]
        if spreads:
            self._arbitrage_metrics.average_spread = np.mean(spreads)
            self._arbitrage_metrics.spread_volatility = np.std(spreads)
    
    def _calculate_profit_factor(self, pnl_values: List[float]) -> float:
        """Calculate profit factor from PnL values."""
        if not pnl_values:
            return 0.0
        
        gross_profit = sum(p for p in pnl_values if p > 0)
        gross_loss = abs(sum(p for p in pnl_values if p < 0))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0.0
        
        return gross_profit / gross_loss
    
    async def _collect_metrics_loop(self) -> None:
        """Main metrics collection loop."""
        while self._is_running:
            try:
                # Collect system metrics if enabled
                if self._enable_system_metrics:
                    await self._collect_system_metrics()
                
                # Update uptime
                uptime_hours = (datetime.now(timezone.utc) - self._start_time).total_seconds() / 3600
                self.record_metric("system.uptime_hours", uptime_hours)
                
                # Wait for next collection
                await asyncio.sleep(self._collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.logger.error(f"Error in metrics collection: {e}")
                await asyncio.sleep(self._collection_interval)
    
    async def _collect_system_metrics(self) -> None:
        """Collect system performance metrics."""
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_system_metric("cpu_usage_percent", cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.record_system_metric("memory_usage_percent", memory.percent)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.record_system_metric("disk_usage_percent", disk_percent)
            
        except ImportError:
            # psutil not available, skip system metrics
            pass
        except Exception as e:
            self._logger.logger.error(f"Error collecting system metrics: {e}")

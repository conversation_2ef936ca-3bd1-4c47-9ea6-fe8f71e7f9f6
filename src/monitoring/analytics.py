"""
Performance analytics for the funding rate arbitrage system.

This module provides comprehensive performance analysis including strategy
performance, risk metrics, and optimization recommendations.
"""

from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, NamedTuple
from dataclasses import dataclass
import json

import pandas as pd
import numpy as np
from scipy import stats

from src.utils.calculations import (
    calculate_sharpe_ratio,
    calculate_max_drawdown,
    calculate_volatility,
)
from src.utils.logging import TradingLogger


@dataclass
class PerformanceReport:
    """Comprehensive performance report."""
    period_start: datetime
    period_end: datetime
    total_return: Decimal
    annualized_return: Decimal
    volatility: Decimal
    sharpe_ratio: Decimal
    sortino_ratio: Decimal
    max_drawdown: Decimal
    calmar_ratio: Decimal
    win_rate: Decimal
    profit_factor: Decimal
    total_trades: int
    average_trade_duration: timedelta
    best_trade: Decimal
    worst_trade: Decimal
    consecutive_wins: int
    consecutive_losses: int
    funding_pnl_contribution: Decimal
    trading_pnl_contribution: Decimal
    total_fees: Decimal
    risk_adjusted_return: Decimal


@dataclass
class RiskMetrics:
    """Risk analysis metrics."""
    var_95: Decimal  # Value at Risk (95%)
    cvar_95: Decimal  # Conditional Value at Risk (95%)
    maximum_leverage: Decimal
    average_leverage: Decimal
    correlation_to_market: Decimal
    beta: Decimal
    tracking_error: Decimal
    information_ratio: Decimal
    downside_deviation: Decimal
    upside_capture: Decimal
    downside_capture: Decimal


@dataclass
class OptimizationRecommendation:
    """Strategy optimization recommendation."""
    parameter: str
    current_value: Any
    recommended_value: Any
    expected_improvement: Decimal
    confidence: Decimal
    reasoning: str


class PerformanceAnalyzer:
    """
    Comprehensive performance analytics engine.
    """
    
    def __init__(self) -> None:
        # Analysis cache
        self._cached_reports: Dict[str, PerformanceReport] = {}
        self._cache_expiry: Dict[str, datetime] = {}
        
        # Benchmark data (would be loaded from external source)
        self._benchmark_returns: Optional[pd.Series] = None
        
        # Logger
        self._logger = TradingLogger("performance_analyzer")
    
    def analyze_performance(
        self,
        trades_df: pd.DataFrame,
        equity_curve_df: pd.DataFrame,
        period_days: int = 30,
    ) -> PerformanceReport:
        """
        Analyze trading performance over a specified period.
        
        Parameters
        ----------
        trades_df : pd.DataFrame
            DataFrame containing trade data.
        equity_curve_df : pd.DataFrame
            DataFrame containing equity curve data.
        period_days : int, default 30
            Analysis period in days.
        
        Returns
        -------
        PerformanceReport
            Comprehensive performance report.
        """
        # Check cache
        cache_key = f"performance_{period_days}_{len(trades_df)}"
        if (cache_key in self._cached_reports and 
            cache_key in self._cache_expiry and
            datetime.now(timezone.utc) < self._cache_expiry[cache_key]):
            return self._cached_reports[cache_key]
        
        # Filter data for analysis period
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=period_days)
        
        period_trades = trades_df[
            (trades_df['entry_time'] >= start_date) & 
            (trades_df['entry_time'] <= end_date)
        ].copy()
        
        period_equity = equity_curve_df[
            (equity_curve_df['timestamp'] >= start_date) & 
            (equity_curve_df['timestamp'] <= end_date)
        ].copy()
        
        # Calculate basic metrics
        total_trades = len(period_trades)
        
        if total_trades == 0:
            # Return empty report if no trades
            return PerformanceReport(
                period_start=start_date,
                period_end=end_date,
                total_return=Decimal("0"),
                annualized_return=Decimal("0"),
                volatility=Decimal("0"),
                sharpe_ratio=Decimal("0"),
                sortino_ratio=Decimal("0"),
                max_drawdown=Decimal("0"),
                calmar_ratio=Decimal("0"),
                win_rate=Decimal("0"),
                profit_factor=Decimal("0"),
                total_trades=0,
                average_trade_duration=timedelta(0),
                best_trade=Decimal("0"),
                worst_trade=Decimal("0"),
                consecutive_wins=0,
                consecutive_losses=0,
                funding_pnl_contribution=Decimal("0"),
                trading_pnl_contribution=Decimal("0"),
                total_fees=Decimal("0"),
                risk_adjusted_return=Decimal("0"),
            )
        
        # Calculate returns
        total_pnl = period_trades['total_pnl'].sum()
        initial_capital = period_equity['equity'].iloc[0] if len(period_equity) > 0 else 10000
        total_return = Decimal(str(total_pnl / initial_capital))
        
        # Annualized return
        years = period_days / 365.25
        annualized_return = ((1 + float(total_return)) ** (1 / years) - 1) if years > 0 else Decimal("0")
        annualized_return = Decimal(str(annualized_return))
        
        # Calculate volatility and risk metrics
        if len(period_equity) > 1:
            returns = period_equity['equity'].pct_change().dropna()
            volatility = Decimal(str(calculate_volatility(returns, annualize=True)))
            
            # Sharpe ratio
            sharpe_ratio = calculate_sharpe_ratio(returns, risk_free_rate=0.02)
            
            # Sortino ratio
            downside_returns = returns[returns < 0]
            downside_std = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 1 else 0
            sortino_ratio = Decimal(str(float(annualized_return) / downside_std)) if downside_std > 0 else Decimal("0")
            
            # Max drawdown
            max_drawdown, _, _ = calculate_max_drawdown(period_equity['equity'])
            
            # Calmar ratio
            calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else Decimal("0")
        else:
            volatility = Decimal("0")
            sharpe_ratio = Decimal("0")
            sortino_ratio = Decimal("0")
            max_drawdown = Decimal("0")
            calmar_ratio = Decimal("0")
        
        # Trading statistics
        winning_trades = len(period_trades[period_trades['total_pnl'] > 0])
        losing_trades = len(period_trades[period_trades['total_pnl'] < 0])
        win_rate = Decimal(str(winning_trades / total_trades)) if total_trades > 0 else Decimal("0")
        
        # Profit factor
        gross_profit = period_trades[period_trades['total_pnl'] > 0]['total_pnl'].sum()
        gross_loss = abs(period_trades[period_trades['total_pnl'] < 0]['total_pnl'].sum())
        profit_factor = Decimal(str(gross_profit / gross_loss)) if gross_loss > 0 else Decimal("999")
        
        # Trade duration
        period_trades['duration'] = pd.to_datetime(period_trades['exit_time']) - pd.to_datetime(period_trades['entry_time'])
        average_trade_duration = period_trades['duration'].mean()
        
        # Best and worst trades
        best_trade = Decimal(str(period_trades['total_pnl'].max()))
        worst_trade = Decimal(str(period_trades['total_pnl'].min()))
        
        # Consecutive wins/losses
        consecutive_wins, consecutive_losses = self._calculate_consecutive_trades(period_trades)
        
        # PnL breakdown
        funding_pnl = period_trades['funding_pnl'].sum()
        trading_pnl = period_trades['trading_pnl'].sum()
        total_fees = period_trades['fees_paid'].sum()
        
        funding_pnl_contribution = Decimal(str(funding_pnl / total_pnl)) if total_pnl != 0 else Decimal("0")
        trading_pnl_contribution = Decimal(str(trading_pnl / total_pnl)) if total_pnl != 0 else Decimal("0")
        
        # Risk-adjusted return
        risk_adjusted_return = total_return / volatility if volatility > 0 else Decimal("0")
        
        # Create report
        report = PerformanceReport(
            period_start=start_date,
            period_end=end_date,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            win_rate=win_rate,
            profit_factor=profit_factor,
            total_trades=total_trades,
            average_trade_duration=average_trade_duration,
            best_trade=best_trade,
            worst_trade=worst_trade,
            consecutive_wins=consecutive_wins,
            consecutive_losses=consecutive_losses,
            funding_pnl_contribution=funding_pnl_contribution,
            trading_pnl_contribution=trading_pnl_contribution,
            total_fees=Decimal(str(total_fees)),
            risk_adjusted_return=risk_adjusted_return,
        )
        
        # Cache report
        self._cached_reports[cache_key] = report
        self._cache_expiry[cache_key] = datetime.now(timezone.utc) + timedelta(hours=1)
        
        return report
    
    def analyze_risk_metrics(
        self,
        trades_df: pd.DataFrame,
        equity_curve_df: pd.DataFrame,
        benchmark_returns: Optional[pd.Series] = None,
    ) -> RiskMetrics:
        """
        Analyze risk metrics for the strategy.
        
        Parameters
        ----------
        trades_df : pd.DataFrame
            DataFrame containing trade data.
        equity_curve_df : pd.DataFrame
            DataFrame containing equity curve data.
        benchmark_returns : pd.Series, optional
            Benchmark returns for comparison.
        
        Returns
        -------
        RiskMetrics
            Comprehensive risk metrics.
        """
        if len(equity_curve_df) < 2:
            # Return empty metrics if insufficient data
            return RiskMetrics(
                var_95=Decimal("0"),
                cvar_95=Decimal("0"),
                maximum_leverage=Decimal("0"),
                average_leverage=Decimal("0"),
                correlation_to_market=Decimal("0"),
                beta=Decimal("0"),
                tracking_error=Decimal("0"),
                information_ratio=Decimal("0"),
                downside_deviation=Decimal("0"),
                upside_capture=Decimal("0"),
                downside_capture=Decimal("0"),
            )
        
        # Calculate returns
        returns = equity_curve_df['equity'].pct_change().dropna()
        
        # Value at Risk (95%)
        var_95 = Decimal(str(returns.quantile(0.05)))
        
        # Conditional Value at Risk (95%)
        cvar_95 = Decimal(str(returns[returns <= returns.quantile(0.05)].mean()))
        
        # Leverage metrics (simplified - would need position size data)
        maximum_leverage = Decimal("3.0")  # Placeholder
        average_leverage = Decimal("1.5")  # Placeholder
        
        # Market correlation and beta
        if benchmark_returns is not None and len(benchmark_returns) > 1:
            # Align returns with benchmark
            aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
            
            if len(aligned_returns) > 1:
                correlation_to_market = Decimal(str(aligned_returns.corr(aligned_benchmark)))
                
                # Calculate beta
                covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
                benchmark_variance = np.var(aligned_benchmark)
                beta = Decimal(str(covariance / benchmark_variance)) if benchmark_variance > 0 else Decimal("0")
                
                # Tracking error
                tracking_error = Decimal(str((aligned_returns - aligned_benchmark).std() * np.sqrt(252)))
                
                # Information ratio
                excess_return = aligned_returns.mean() - aligned_benchmark.mean()
                information_ratio = Decimal(str(excess_return / tracking_error)) if tracking_error > 0 else Decimal("0")
                
                # Upside/downside capture
                up_market = aligned_benchmark > 0
                down_market = aligned_benchmark < 0
                
                if up_market.sum() > 0:
                    upside_capture = Decimal(str(aligned_returns[up_market].mean() / aligned_benchmark[up_market].mean()))
                else:
                    upside_capture = Decimal("0")
                
                if down_market.sum() > 0:
                    downside_capture = Decimal(str(aligned_returns[down_market].mean() / aligned_benchmark[down_market].mean()))
                else:
                    downside_capture = Decimal("0")
            else:
                correlation_to_market = Decimal("0")
                beta = Decimal("0")
                tracking_error = Decimal("0")
                information_ratio = Decimal("0")
                upside_capture = Decimal("0")
                downside_capture = Decimal("0")
        else:
            correlation_to_market = Decimal("0")
            beta = Decimal("0")
            tracking_error = Decimal("0")
            information_ratio = Decimal("0")
            upside_capture = Decimal("0")
            downside_capture = Decimal("0")
        
        # Downside deviation
        downside_returns = returns[returns < returns.mean()]
        downside_deviation = Decimal(str(downside_returns.std() * np.sqrt(252))) if len(downside_returns) > 1 else Decimal("0")
        
        return RiskMetrics(
            var_95=var_95,
            cvar_95=cvar_95,
            maximum_leverage=maximum_leverage,
            average_leverage=average_leverage,
            correlation_to_market=correlation_to_market,
            beta=beta,
            tracking_error=tracking_error,
            information_ratio=information_ratio,
            downside_deviation=downside_deviation,
            upside_capture=upside_capture,
            downside_capture=downside_capture,
        )
    
    def generate_optimization_recommendations(
        self,
        performance_report: PerformanceReport,
        risk_metrics: RiskMetrics,
        current_config: Dict[str, Any],
    ) -> List[OptimizationRecommendation]:
        """
        Generate strategy optimization recommendations.
        
        Parameters
        ----------
        performance_report : PerformanceReport
            Current performance report.
        risk_metrics : RiskMetrics
            Current risk metrics.
        current_config : Dict[str, Any]
            Current strategy configuration.
        
        Returns
        -------
        List[OptimizationRecommendation]
            List of optimization recommendations.
        """
        recommendations = []
        
        # Win rate optimization
        if performance_report.win_rate < Decimal("0.5"):
            recommendations.append(OptimizationRecommendation(
                parameter="min_funding_rate_threshold",
                current_value=current_config.get("min_funding_rate_threshold", 0.01),
                recommended_value=current_config.get("min_funding_rate_threshold", 0.01) * 1.2,
                expected_improvement=Decimal("0.05"),
                confidence=Decimal("0.7"),
                reasoning="Low win rate suggests threshold may be too aggressive. Increasing threshold should improve trade quality.",
            ))
        
        # Sharpe ratio optimization
        if performance_report.sharpe_ratio < Decimal("1.0"):
            recommendations.append(OptimizationRecommendation(
                parameter="position_size_percentage",
                current_value=current_config.get("position_size_percentage", 0.1),
                recommended_value=current_config.get("position_size_percentage", 0.1) * 0.8,
                expected_improvement=Decimal("0.1"),
                confidence=Decimal("0.6"),
                reasoning="Low Sharpe ratio suggests risk-adjusted returns could be improved by reducing position size.",
            ))
        
        # Max drawdown optimization
        if performance_report.max_drawdown > Decimal("0.15"):
            recommendations.append(OptimizationRecommendation(
                parameter="stop_loss_percentage",
                current_value=current_config.get("stop_loss_percentage", 0.05),
                recommended_value=current_config.get("stop_loss_percentage", 0.05) * 0.8,
                expected_improvement=Decimal("0.03"),
                confidence=Decimal("0.8"),
                reasoning="High maximum drawdown suggests tighter risk controls are needed.",
            ))
        
        # Profit factor optimization
        if performance_report.profit_factor < Decimal("1.5"):
            recommendations.append(OptimizationRecommendation(
                parameter="min_arbitrage_profit",
                current_value=current_config.get("min_arbitrage_profit", 0.005),
                recommended_value=current_config.get("min_arbitrage_profit", 0.005) * 1.3,
                expected_improvement=Decimal("0.2"),
                confidence=Decimal("0.7"),
                reasoning="Low profit factor suggests minimum profit threshold should be increased to filter out marginal trades.",
            ))
        
        return recommendations
    
    def _calculate_consecutive_trades(self, trades_df: pd.DataFrame) -> tuple[int, int]:
        """Calculate maximum consecutive wins and losses."""
        if len(trades_df) == 0:
            return 0, 0
        
        # Sort by entry time
        sorted_trades = trades_df.sort_values('entry_time')
        
        # Create win/loss sequence
        wins_losses = (sorted_trades['total_pnl'] > 0).astype(int)
        
        # Calculate consecutive sequences
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_wins = 0
        current_losses = 0
        
        for is_win in wins_losses:
            if is_win:
                current_wins += 1
                current_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, current_losses)
        
        return max_consecutive_wins, max_consecutive_losses
    
    def export_report(self, report: PerformanceReport, format: str = "json") -> str:
        """
        Export performance report in specified format.
        
        Parameters
        ----------
        report : PerformanceReport
            Performance report to export.
        format : str, default "json"
            Export format ("json", "csv", "html").
        
        Returns
        -------
        str
            Exported report as string.
        """
        if format == "json":
            # Convert to JSON-serializable format
            report_dict = {
                "period_start": report.period_start.isoformat(),
                "period_end": report.period_end.isoformat(),
                "total_return": float(report.total_return),
                "annualized_return": float(report.annualized_return),
                "volatility": float(report.volatility),
                "sharpe_ratio": float(report.sharpe_ratio),
                "sortino_ratio": float(report.sortino_ratio),
                "max_drawdown": float(report.max_drawdown),
                "calmar_ratio": float(report.calmar_ratio),
                "win_rate": float(report.win_rate),
                "profit_factor": float(report.profit_factor),
                "total_trades": report.total_trades,
                "average_trade_duration_hours": report.average_trade_duration.total_seconds() / 3600,
                "best_trade": float(report.best_trade),
                "worst_trade": float(report.worst_trade),
                "consecutive_wins": report.consecutive_wins,
                "consecutive_losses": report.consecutive_losses,
                "funding_pnl_contribution": float(report.funding_pnl_contribution),
                "trading_pnl_contribution": float(report.trading_pnl_contribution),
                "total_fees": float(report.total_fees),
                "risk_adjusted_return": float(report.risk_adjusted_return),
            }
            return json.dumps(report_dict, indent=2)
        
        elif format == "html":
            # Generate HTML report
            html = f"""
            <html>
            <head><title>Performance Report</title></head>
            <body>
            <h1>Performance Report</h1>
            <p><strong>Period:</strong> {report.period_start.date()} to {report.period_end.date()}</p>
            <h2>Returns</h2>
            <ul>
                <li>Total Return: {report.total_return:.2%}</li>
                <li>Annualized Return: {report.annualized_return:.2%}</li>
                <li>Volatility: {report.volatility:.2%}</li>
            </ul>
            <h2>Risk Metrics</h2>
            <ul>
                <li>Sharpe Ratio: {report.sharpe_ratio:.2f}</li>
                <li>Sortino Ratio: {report.sortino_ratio:.2f}</li>
                <li>Max Drawdown: {report.max_drawdown:.2%}</li>
                <li>Calmar Ratio: {report.calmar_ratio:.2f}</li>
            </ul>
            <h2>Trading Statistics</h2>
            <ul>
                <li>Total Trades: {report.total_trades}</li>
                <li>Win Rate: {report.win_rate:.2%}</li>
                <li>Profit Factor: {report.profit_factor:.2f}</li>
                <li>Best Trade: ${report.best_trade:,.2f}</li>
                <li>Worst Trade: ${report.worst_trade:,.2f}</li>
            </ul>
            </body>
            </html>
            """
            return html
        
        else:
            raise ValueError(f"Unsupported export format: {format}")

    def compare_periods(
        self,
        current_report: PerformanceReport,
        previous_report: PerformanceReport,
    ) -> Dict[str, Any]:
        """
        Compare performance between two periods.

        Parameters
        ----------
        current_report : PerformanceReport
            Current period performance.
        previous_report : PerformanceReport
            Previous period performance.

        Returns
        -------
        Dict[str, Any]
            Performance comparison metrics.
        """
        comparison = {
            "total_return_change": float(current_report.total_return - previous_report.total_return),
            "sharpe_ratio_change": float(current_report.sharpe_ratio - previous_report.sharpe_ratio),
            "win_rate_change": float(current_report.win_rate - previous_report.win_rate),
            "max_drawdown_change": float(current_report.max_drawdown - previous_report.max_drawdown),
            "profit_factor_change": float(current_report.profit_factor - previous_report.profit_factor),
            "trade_count_change": current_report.total_trades - previous_report.total_trades,
            "volatility_change": float(current_report.volatility - previous_report.volatility),
        }

        # Calculate percentage changes
        for metric in ["total_return", "sharpe_ratio", "win_rate", "profit_factor", "volatility"]:
            current_val = getattr(current_report, metric)
            previous_val = getattr(previous_report, metric)

            if previous_val != 0:
                pct_change = ((current_val - previous_val) / previous_val) * 100
                comparison[f"{metric}_pct_change"] = float(pct_change)
            else:
                comparison[f"{metric}_pct_change"] = 0.0

        return comparison

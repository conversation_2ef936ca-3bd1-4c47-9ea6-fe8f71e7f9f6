"""
Live order manager for the funding rate arbitrage system.

This module provides advanced order management including order routing,
execution monitoring, and trade reconciliation for live trading.
"""

import asyncio
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Callable, NamedTuple
from enum import Enum
from uuid import uuid4

from nautilus_trader.model.orders import Order
from nautilus_trader.model.events import OrderEvent, OrderFilled, OrderCanceled, OrderRejected
from nautilus_trader.model.identifiers import ClientOrderId, InstrumentId
from nautilus_trader.model.enums import OrderSide, OrderStatus
from nautilus_trader.model.objects import Price, Quantity

from src.utils.logging import TradingLogger


class OrderState(Enum):
    """Order state enumeration."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class OrderInfo(NamedTuple):
    """Order information tracking."""
    order_id: str
    client_order_id: ClientOrderId
    instrument_id: InstrumentId
    side: OrderSide
    quantity: Quantity
    price: Optional[Price]
    state: OrderState
    submit_time: datetime
    fill_time: Optional[datetime] = None
    filled_quantity: Quantity = Quantity.zero()
    average_fill_price: Optional[Price] = None
    fees_paid: Decimal = Decimal("0")
    slippage: Decimal = Decimal("0")
    execution_time_ms: Optional[int] = None


class ArbitrageOrderGroup:
    """Group of orders for an arbitrage trade."""
    
    def __init__(
        self,
        group_id: str,
        symbol: str,
        arbitrage_type: str,
    ) -> None:
        self.group_id = group_id
        self.symbol = symbol
        self.arbitrage_type = arbitrage_type
        self.created_time = datetime.now(timezone.utc)
        
        # Orders in the group
        self.spot_order: Optional[OrderInfo] = None
        self.futures_order: Optional[OrderInfo] = None
        
        # Group state
        self.is_complete = False
        self.is_canceled = False
        self.completion_time: Optional[datetime] = None
        
        # Performance tracking
        self.total_slippage = Decimal("0")
        self.total_fees = Decimal("0")
        self.execution_time_ms: Optional[int] = None
    
    def add_order(self, order_info: OrderInfo, order_type: str) -> None:
        """Add an order to the group."""
        if order_type == "spot":
            self.spot_order = order_info
        elif order_type == "futures":
            self.futures_order = order_info
    
    def is_group_complete(self) -> bool:
        """Check if all orders in the group are filled."""
        return (self.spot_order and self.spot_order.state == OrderState.FILLED and
                self.futures_order and self.futures_order.state == OrderState.FILLED)
    
    def update_completion_status(self) -> None:
        """Update group completion status."""
        if self.is_group_complete() and not self.is_complete:
            self.is_complete = True
            self.completion_time = datetime.now(timezone.utc)
            
            # Calculate total execution time
            if self.spot_order and self.futures_order:
                start_time = min(self.spot_order.submit_time, self.futures_order.submit_time)
                end_time = max(
                    self.spot_order.fill_time or self.spot_order.submit_time,
                    self.futures_order.fill_time or self.futures_order.submit_time,
                )
                self.execution_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Calculate totals
            if self.spot_order:
                self.total_fees += self.spot_order.fees_paid
                self.total_slippage += self.spot_order.slippage
            
            if self.futures_order:
                self.total_fees += self.futures_order.fees_paid
                self.total_slippage += self.futures_order.slippage


class LiveOrderManager:
    """
    Advanced order manager for live arbitrage trading.
    """
    
    def __init__(
        self,
        max_order_age_minutes: int = 60,
        enable_order_monitoring: bool = True,
    ) -> None:
        self._max_order_age_minutes = max_order_age_minutes
        self._enable_order_monitoring = enable_order_monitoring
        
        # Order tracking
        self._orders: Dict[str, OrderInfo] = {}
        self._order_groups: Dict[str, ArbitrageOrderGroup] = {}
        self._pending_orders: Dict[str, OrderInfo] = {}
        
        # Event callbacks
        self._fill_callbacks: List[Callable] = []
        self._group_complete_callbacks: List[Callable] = []
        self._order_error_callbacks: List[Callable] = []
        
        # Monitoring
        self._monitoring_task: Optional[asyncio.Task] = None
        self._is_running = False
        
        # Performance tracking
        self._execution_stats: Dict[str, List[float]] = {
            "execution_times": [],
            "slippage": [],
            "fees": [],
        }
        
        # Logger
        self._logger = TradingLogger("live_order_manager")
    
    def add_fill_callback(self, callback: Callable) -> None:
        """Add callback for order fills."""
        self._fill_callbacks.append(callback)
    
    def add_group_complete_callback(self, callback: Callable) -> None:
        """Add callback for completed arbitrage groups."""
        self._group_complete_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable) -> None:
        """Add callback for order errors."""
        self._order_error_callbacks.append(callback)
    
    async def start(self) -> None:
        """Start the order manager."""
        if self._is_running:
            return
        
        self._logger.logger.info("Starting live order manager")
        
        if self._enable_order_monitoring:
            self._monitoring_task = asyncio.create_task(self._monitor_orders())
        
        self._is_running = True
        
        self._logger.logger.info("Live order manager started")
    
    async def stop(self) -> None:
        """Stop the order manager."""
        if not self._is_running:
            return
        
        self._logger.logger.info("Stopping live order manager")
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        self._is_running = False
        
        self._logger.logger.info("Live order manager stopped")
    
    def create_arbitrage_group(
        self,
        symbol: str,
        arbitrage_type: str,
    ) -> str:
        """Create a new arbitrage order group."""
        group_id = f"arb_{symbol}_{arbitrage_type}_{uuid4().hex[:8]}"
        
        group = ArbitrageOrderGroup(
            group_id=group_id,
            symbol=symbol,
            arbitrage_type=arbitrage_type,
        )
        
        self._order_groups[group_id] = group
        
        self._logger.logger.info(
            f"Created arbitrage order group {group_id}",
            symbol=symbol,
            arbitrage_type=arbitrage_type,
        )
        
        return group_id
    
    def register_order(
        self,
        order: Order,
        group_id: Optional[str] = None,
        order_type: Optional[str] = None,
    ) -> None:
        """Register an order for tracking."""
        order_info = OrderInfo(
            order_id=str(order.client_order_id),
            client_order_id=order.client_order_id,
            instrument_id=order.instrument_id,
            side=order.side,
            quantity=order.quantity,
            price=getattr(order, 'price', None),
            state=OrderState.PENDING,
            submit_time=datetime.now(timezone.utc),
        )
        
        self._orders[order_info.order_id] = order_info
        self._pending_orders[order_info.order_id] = order_info
        
        # Add to group if specified
        if group_id and group_id in self._order_groups and order_type:
            self._order_groups[group_id].add_order(order_info, order_type)
        
        self._logger.logger.info(
            f"Registered order {order_info.order_id}",
            instrument=str(order.instrument_id),
            side=order.side.name,
            quantity=float(order.quantity),
            group_id=group_id,
        )
    
    def handle_order_event(self, event: OrderEvent) -> None:
        """Handle order events from the trading system."""
        order_id = str(event.client_order_id)
        
        if order_id not in self._orders:
            self._logger.logger.warning(f"Received event for unknown order {order_id}")
            return
        
        order_info = self._orders[order_id]
        
        if isinstance(event, OrderFilled):
            self._handle_order_filled(order_info, event)
        elif isinstance(event, OrderCanceled):
            self._handle_order_canceled(order_info, event)
        elif isinstance(event, OrderRejected):
            self._handle_order_rejected(order_info, event)
    
    def _handle_order_filled(self, order_info: OrderInfo, event: OrderFilled) -> None:
        """Handle order fill event."""
        # Calculate slippage
        slippage = Decimal("0")
        if order_info.price and event.last_px:
            if order_info.side == OrderSide.BUY:
                slippage = (event.last_px - order_info.price) / order_info.price
            else:
                slippage = (order_info.price - event.last_px) / order_info.price
        
        # Update order info
        updated_order = order_info._replace(
            state=OrderState.FILLED,
            fill_time=datetime.now(timezone.utc),
            filled_quantity=event.last_qty,
            average_fill_price=event.last_px,
            slippage=slippage,
            execution_time_ms=int((datetime.now(timezone.utc) - order_info.submit_time).total_seconds() * 1000),
        )
        
        self._orders[order_info.order_id] = updated_order
        
        # Remove from pending
        if order_info.order_id in self._pending_orders:
            del self._pending_orders[order_info.order_id]
        
        # Update execution stats
        self._execution_stats["execution_times"].append(updated_order.execution_time_ms or 0)
        self._execution_stats["slippage"].append(float(slippage))
        
        # Trigger callbacks
        for callback in self._fill_callbacks:
            try:
                callback(updated_order, event)
            except Exception as e:
                self._logger.logger.error(f"Error in fill callback: {e}")
        
        # Check if any arbitrage groups are complete
        self._check_group_completions()
        
        self._logger.logger.info(
            f"Order filled: {order_info.order_id}",
            fill_price=float(event.last_px) if event.last_px else None,
            fill_quantity=float(event.last_qty),
            slippage=float(slippage),
            execution_time_ms=updated_order.execution_time_ms,
        )
    
    def _handle_order_canceled(self, order_info: OrderInfo, event: OrderCanceled) -> None:
        """Handle order cancel event."""
        updated_order = order_info._replace(state=OrderState.CANCELED)
        self._orders[order_info.order_id] = updated_order
        
        # Remove from pending
        if order_info.order_id in self._pending_orders:
            del self._pending_orders[order_info.order_id]
        
        self._logger.logger.info(f"Order canceled: {order_info.order_id}")
    
    def _handle_order_rejected(self, order_info: OrderInfo, event: OrderRejected) -> None:
        """Handle order reject event."""
        updated_order = order_info._replace(state=OrderState.REJECTED)
        self._orders[order_info.order_id] = updated_order
        
        # Remove from pending
        if order_info.order_id in self._pending_orders:
            del self._pending_orders[order_info.order_id]
        
        # Trigger error callbacks
        for callback in self._order_error_callbacks:
            try:
                callback(updated_order, event)
            except Exception as e:
                self._logger.logger.error(f"Error in error callback: {e}")
        
        self._logger.logger.error(
            f"Order rejected: {order_info.order_id}",
            reason=event.reason,
        )
    
    def _check_group_completions(self) -> None:
        """Check for completed arbitrage groups."""
        for group in self._order_groups.values():
            if not group.is_complete and group.is_group_complete():
                group.update_completion_status()
                
                # Trigger callbacks
                for callback in self._group_complete_callbacks:
                    try:
                        callback(group)
                    except Exception as e:
                        self._logger.logger.error(f"Error in group complete callback: {e}")
                
                self._logger.logger.info(
                    f"Arbitrage group completed: {group.group_id}",
                    execution_time_ms=group.execution_time_ms,
                    total_fees=float(group.total_fees),
                    total_slippage=float(group.total_slippage),
                )
    
    async def _monitor_orders(self) -> None:
        """Monitor orders for timeouts and issues."""
        while self._is_running:
            try:
                current_time = datetime.now(timezone.utc)
                timeout_threshold = current_time - timedelta(minutes=self._max_order_age_minutes)
                
                # Check for old pending orders
                old_orders = [
                    order for order in self._pending_orders.values()
                    if order.submit_time < timeout_threshold
                ]
                
                for order in old_orders:
                    self._logger.logger.warning(
                        f"Order {order.order_id} has been pending for over {self._max_order_age_minutes} minutes"
                    )
                    
                    # Trigger error callback
                    for callback in self._order_error_callbacks:
                        try:
                            callback(order, "timeout")
                        except Exception as e:
                            self._logger.logger.error(f"Error in timeout callback: {e}")
                
                # Wait before next check
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self._logger.logger.error(f"Error in order monitoring: {e}")
                await asyncio.sleep(60)
    
    def get_order_status(self, order_id: str) -> Optional[OrderInfo]:
        """Get status of a specific order."""
        return self._orders.get(order_id)
    
    def get_group_status(self, group_id: str) -> Optional[ArbitrageOrderGroup]:
        """Get status of an arbitrage group."""
        return self._order_groups.get(group_id)
    
    def get_execution_stats(self) -> Dict:
        """Get execution performance statistics."""
        stats = {}
        
        for metric, values in self._execution_stats.items():
            if values:
                stats[metric] = {
                    "count": len(values),
                    "mean": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                }
            else:
                stats[metric] = {"count": 0}
        
        return stats
    
    def get_manager_status(self) -> Dict:
        """Get order manager status."""
        return {
            "is_running": self._is_running,
            "total_orders": len(self._orders),
            "pending_orders": len(self._pending_orders),
            "active_groups": len([g for g in self._order_groups.values() if not g.is_complete]),
            "completed_groups": len([g for g in self._order_groups.values() if g.is_complete]),
            "execution_stats": self.get_execution_stats(),
        }

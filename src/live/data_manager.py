"""
Live data manager for the funding rate arbitrage system.

This module provides real-time data management including market data feeds,
funding rate monitoring, and data quality checks for live trading.
"""

import asyncio
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Callable, Set
from collections import deque

from nautilus_trader.adapters.binance.common.enums import BinanceAccountType
from nautilus_trader.adapters.binance.futures.http.client import BinanceFuturesHttpClient
from nautilus_trader.adapters.binance.spot.http.client import BinanceSpotHttpClient
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.data import QuoteTick, TradeTick

from src.data.funding_rate import FundingRateData, FundingRateProvider
from src.data.market_data import MarketDataManager
from src.utils.time_utils import get_next_funding_time, time_to_funding
from src.utils.logging import TradingLogger


class DataQualityMonitor:
    """Monitor data quality and detect issues."""
    
    def __init__(self, max_history: int = 1000) -> None:
        self._max_history = max_history
        
        # Data quality metrics
        self._quote_timestamps: Dict[str, deque] = {}
        self._trade_timestamps: Dict[str, deque] = {}
        self._funding_timestamps: Dict[str, deque] = {}
        
        # Quality thresholds
        self._max_quote_gap_seconds = 10  # Max gap between quotes
        self._max_trade_gap_seconds = 60  # Max gap between trades
        self._max_funding_gap_hours = 9   # Max gap between funding updates
        
        # Alert callbacks
        self._alert_callbacks: List[Callable] = []
    
    def add_alert_callback(self, callback: Callable) -> None:
        """Add callback for data quality alerts."""
        self._alert_callbacks.append(callback)
    
    def record_quote(self, symbol: str, timestamp: datetime) -> None:
        """Record quote timestamp for quality monitoring."""
        if symbol not in self._quote_timestamps:
            self._quote_timestamps[symbol] = deque(maxlen=self._max_history)
        
        self._quote_timestamps[symbol].append(timestamp)
        self._check_quote_quality(symbol)
    
    def record_trade(self, symbol: str, timestamp: datetime) -> None:
        """Record trade timestamp for quality monitoring."""
        if symbol not in self._trade_timestamps:
            self._trade_timestamps[symbol] = deque(maxlen=self._max_history)
        
        self._trade_timestamps[symbol].append(timestamp)
        self._check_trade_quality(symbol)
    
    def record_funding_update(self, symbol: str, timestamp: datetime) -> None:
        """Record funding rate update timestamp."""
        if symbol not in self._funding_timestamps:
            self._funding_timestamps[symbol] = deque(maxlen=self._max_history)
        
        self._funding_timestamps[symbol].append(timestamp)
        self._check_funding_quality(symbol)
    
    def _check_quote_quality(self, symbol: str) -> None:
        """Check quote data quality."""
        timestamps = self._quote_timestamps[symbol]
        if len(timestamps) < 2:
            return
        
        latest = timestamps[-1]
        previous = timestamps[-2]
        gap = (latest - previous).total_seconds()
        
        if gap > self._max_quote_gap_seconds:
            self._trigger_alert(
                "quote_gap",
                symbol,
                f"Quote gap of {gap:.1f}s exceeds threshold of {self._max_quote_gap_seconds}s"
            )
    
    def _check_trade_quality(self, symbol: str) -> None:
        """Check trade data quality."""
        timestamps = self._trade_timestamps[symbol]
        if len(timestamps) < 2:
            return
        
        latest = timestamps[-1]
        previous = timestamps[-2]
        gap = (latest - previous).total_seconds()
        
        if gap > self._max_trade_gap_seconds:
            self._trigger_alert(
                "trade_gap",
                symbol,
                f"Trade gap of {gap:.1f}s exceeds threshold of {self._max_trade_gap_seconds}s"
            )
    
    def _check_funding_quality(self, symbol: str) -> None:
        """Check funding rate data quality."""
        timestamps = self._funding_timestamps[symbol]
        if len(timestamps) < 2:
            return
        
        latest = timestamps[-1]
        previous = timestamps[-2]
        gap = (latest - previous).total_seconds() / 3600  # Convert to hours
        
        if gap > self._max_funding_gap_hours:
            self._trigger_alert(
                "funding_gap",
                symbol,
                f"Funding rate gap of {gap:.1f}h exceeds threshold of {self._max_funding_gap_hours}h"
            )
    
    def _trigger_alert(self, alert_type: str, symbol: str, message: str) -> None:
        """Trigger data quality alert."""
        for callback in self._alert_callbacks:
            callback(alert_type, symbol, message)


class LiveDataManager:
    """
    Live data manager for real-time market data and funding rates.
    """
    
    def __init__(
        self,
        spot_client: BinanceSpotHttpClient,
        futures_client: BinanceFuturesHttpClient,
        instruments: List[str],
        enable_quality_monitoring: bool = True,
    ) -> None:
        self._spot_client = spot_client
        self._futures_client = futures_client
        self._instruments = instruments
        self._enable_quality_monitoring = enable_quality_monitoring
        
        # Core components
        self._funding_rate_provider = FundingRateProvider(futures_client, instruments)
        self._market_data_manager = MarketDataManager(
            spot_client=None,  # Will be set up with live clients
            futures_client=None,
            funding_rate_provider=self._funding_rate_provider,
            instruments=instruments,
        )
        
        # Data quality monitoring
        self._quality_monitor = DataQualityMonitor() if enable_quality_monitoring else None
        
        # State
        self._is_running = False
        self._subscribed_instruments: Set[str] = set()
        
        # Data callbacks
        self._data_callbacks: List[Callable] = []
        self._funding_callbacks: List[Callable] = []
        self._quality_callbacks: List[Callable] = []
        
        # Monitoring tasks
        self._monitoring_tasks: List[asyncio.Task] = []
        
        # Logger
        self._logger = TradingLogger("live_data_manager")
    
    def add_data_callback(self, callback: Callable) -> None:
        """Add callback for market data updates."""
        self._data_callbacks.append(callback)
    
    def add_funding_callback(self, callback: Callable) -> None:
        """Add callback for funding rate updates."""
        self._funding_callbacks.append(callback)
    
    def add_quality_callback(self, callback: Callable) -> None:
        """Add callback for data quality alerts."""
        self._quality_callbacks.append(callback)
        if self._quality_monitor:
            self._quality_monitor.add_alert_callback(callback)
    
    async def start(self) -> None:
        """Start live data feeds."""
        if self._is_running:
            self._logger.logger.warning("Data manager is already running")
            return
        
        self._logger.logger.info("Starting live data manager")
        
        try:
            # Start market data manager
            await self._market_data_manager.start()
            
            # Subscribe to instruments
            for symbol in self._instruments:
                await self._subscribe_instrument(symbol)
            
            # Start monitoring tasks
            self._start_monitoring_tasks()
            
            self._is_running = True
            
            self._logger.logger.info(
                f"Live data manager started for {len(self._instruments)} instruments",
                instruments=self._instruments,
            )
            
        except Exception as e:
            self._logger.logger.error(f"Failed to start data manager: {e}")
            await self.stop()
            raise
    
    async def stop(self) -> None:
        """Stop live data feeds."""
        if not self._is_running:
            return
        
        self._logger.logger.info("Stopping live data manager")
        
        try:
            # Cancel monitoring tasks
            for task in self._monitoring_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            if self._monitoring_tasks:
                await asyncio.gather(*self._monitoring_tasks, return_exceptions=True)
            
            # Stop market data manager
            await self._market_data_manager.stop()
            
            self._is_running = False
            
            self._logger.logger.info("Live data manager stopped")
            
        except Exception as e:
            self._logger.logger.error(f"Error stopping data manager: {e}")
    
    async def _subscribe_instrument(self, symbol: str) -> None:
        """Subscribe to data feeds for an instrument."""
        if symbol in self._subscribed_instruments:
            return
        
        try:
            # Subscribe to market data through market data manager
            # The actual subscription will be handled by NautilusTrader adapters
            
            self._subscribed_instruments.add(symbol)
            
            self._logger.logger.info(f"Subscribed to data feeds for {symbol}")
            
        except Exception as e:
            self._logger.logger.error(f"Failed to subscribe to {symbol}: {e}")
    
    async def _unsubscribe_instrument(self, symbol: str) -> None:
        """Unsubscribe from data feeds for an instrument."""
        if symbol not in self._subscribed_instruments:
            return
        
        try:
            # Unsubscribe from market data
            # The actual unsubscription will be handled by NautilusTrader adapters
            
            self._subscribed_instruments.remove(symbol)
            
            self._logger.logger.info(f"Unsubscribed from data feeds for {symbol}")
            
        except Exception as e:
            self._logger.logger.error(f"Failed to unsubscribe from {symbol}: {e}")
    
    def _start_monitoring_tasks(self) -> None:
        """Start background monitoring tasks."""
        # Funding rate monitoring
        funding_task = asyncio.create_task(self._monitor_funding_rates())
        self._monitoring_tasks.append(funding_task)
        
        # Data quality monitoring
        if self._enable_quality_monitoring:
            quality_task = asyncio.create_task(self._monitor_data_quality())
            self._monitoring_tasks.append(quality_task)
        
        # Connection health monitoring
        health_task = asyncio.create_task(self._monitor_connection_health())
        self._monitoring_tasks.append(health_task)
    
    async def _monitor_funding_rates(self) -> None:
        """Monitor funding rates and trigger callbacks."""
        while self._is_running:
            try:
                # Get current funding rates
                funding_rates = await self._funding_rate_provider.get_current_funding_rates()
                
                # Process each funding rate
                for symbol, funding_data in funding_rates.items():
                    # Record for quality monitoring
                    if self._quality_monitor:
                        self._quality_monitor.record_funding_update(
                            symbol, datetime.now(timezone.utc)
                        )
                    
                    # Trigger callbacks
                    for callback in self._funding_callbacks:
                        try:
                            callback(funding_data)
                        except Exception as e:
                            self._logger.logger.error(f"Error in funding callback: {e}")
                
                # Wait before next update (5 minutes)
                await asyncio.sleep(300)
                
            except Exception as e:
                self._logger.logger.error(f"Error monitoring funding rates: {e}")
                await asyncio.sleep(60)  # Retry after 1 minute on error
    
    async def _monitor_data_quality(self) -> None:
        """Monitor data quality and trigger alerts."""
        while self._is_running:
            try:
                # Check for stale data
                current_time = datetime.now(timezone.utc)
                
                for symbol in self._subscribed_instruments:
                    # Check last quote time
                    last_quote = self._market_data_manager.get_spot_quote(symbol)
                    if last_quote:
                        quote_age = (current_time - last_quote.ts_init).total_seconds()
                        if quote_age > 30:  # 30 seconds threshold
                            for callback in self._quality_callbacks:
                                callback(
                                    "stale_quote",
                                    symbol,
                                    f"Quote data is {quote_age:.1f}s old"
                                )
                    
                    # Check last trade time
                    last_trade = self._market_data_manager.get_spot_trade(symbol)
                    if last_trade:
                        trade_age = (current_time - last_trade.ts_init).total_seconds()
                        if trade_age > 120:  # 2 minutes threshold
                            for callback in self._quality_callbacks:
                                callback(
                                    "stale_trade",
                                    symbol,
                                    f"Trade data is {trade_age:.1f}s old"
                                )
                
                # Wait before next check
                await asyncio.sleep(30)
                
            except Exception as e:
                self._logger.logger.error(f"Error monitoring data quality: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_connection_health(self) -> None:
        """Monitor connection health and reconnect if needed."""
        while self._is_running:
            try:
                # Check connection health
                # This would typically involve pinging the exchange or checking heartbeats
                
                # For now, just log that we're monitoring
                self._logger.logger.debug("Connection health check passed")
                
                # Wait before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                self._logger.logger.error(f"Connection health check failed: {e}")
                
                # Attempt to reconnect
                await self._attempt_reconnection()
                
                await asyncio.sleep(30)
    
    async def _attempt_reconnection(self) -> None:
        """Attempt to reconnect data feeds."""
        self._logger.logger.warning("Attempting to reconnect data feeds")
        
        try:
            # Stop and restart market data manager
            await self._market_data_manager.stop()
            await asyncio.sleep(5)  # Wait before restart
            await self._market_data_manager.start()
            
            # Re-subscribe to instruments
            for symbol in list(self._subscribed_instruments):
                self._subscribed_instruments.remove(symbol)
                await self._subscribe_instrument(symbol)
            
            self._logger.logger.info("Data feeds reconnected successfully")
            
        except Exception as e:
            self._logger.logger.error(f"Failed to reconnect data feeds: {e}")
    
    def get_data_status(self) -> Dict:
        """Get current data status."""
        current_time = datetime.now(timezone.utc)
        
        status = {
            "is_running": self._is_running,
            "subscribed_instruments": list(self._subscribed_instruments),
            "timestamp": current_time,
            "instruments": {},
        }
        
        # Get status for each instrument
        for symbol in self._subscribed_instruments:
            instrument_status = {
                "spot_quote_available": self._market_data_manager.get_spot_quote(symbol) is not None,
                "futures_quote_available": self._market_data_manager.get_futures_quote(symbol) is not None,
                "funding_rate_available": self._market_data_manager.get_funding_rate(symbol) is not None,
            }
            
            # Add data freshness info
            spot_quote = self._market_data_manager.get_spot_quote(symbol)
            if spot_quote:
                quote_age = (current_time - spot_quote.ts_init).total_seconds()
                instrument_status["spot_quote_age_seconds"] = quote_age
            
            futures_quote = self._market_data_manager.get_futures_quote(symbol)
            if futures_quote:
                quote_age = (current_time - futures_quote.ts_init).total_seconds()
                instrument_status["futures_quote_age_seconds"] = quote_age
            
            funding_rate = self._market_data_manager.get_funding_rate(symbol)
            if funding_rate:
                rate_age = (current_time.timestamp() * 1000 - funding_rate.funding_time) / 1000
                instrument_status["funding_rate_age_seconds"] = rate_age
            
            status["instruments"][symbol] = instrument_status
        
        return status

    async def add_instrument(self, symbol: str) -> None:
        """Add a new instrument to monitor."""
        if symbol in self._instruments:
            self._logger.logger.warning(f"Instrument {symbol} already being monitored")
            return

        self._instruments.append(symbol)

        if self._is_running:
            await self._subscribe_instrument(symbol)

        self._logger.logger.info(f"Added instrument {symbol} to monitoring")

    async def remove_instrument(self, symbol: str) -> None:
        """Remove an instrument from monitoring."""
        if symbol not in self._instruments:
            self._logger.logger.warning(f"Instrument {symbol} not being monitored")
            return

        self._instruments.remove(symbol)

        if self._is_running:
            await self._unsubscribe_instrument(symbol)

        self._logger.logger.info(f"Removed instrument {symbol} from monitoring")

"""
Live trading node for the funding rate arbitrage system.

This module provides the main trading node that orchestrates all components
for live trading including data feeds, strategy execution, and risk management.
"""

import asyncio
from decimal import Decimal
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pathlib import Path

from nautilus_trader.live.node import TradingNode
from nautilus_trader.config import TradingNodeConfig
from nautilus_trader.adapters.binance.config import BinanceDataClientConfig, BinanceExecClientConfig
from nautilus_trader.adapters.binance.factories import BinanceLiveDataClientFactory, BinanceLiveExecClientFactory
from nautilus_trader.model.identifiers import InstrumentId, ClientId

from src.config import Config
from src.strategies.funding_arbitrage import FundingRateArbitrageStrategy, FundingRateArbitrageConfig
from src.strategies.analysis_engine import FundingRateAnalysisEngine
from src.data.funding_rate import FundingRateProvider
from src.data.market_data import MarketDataManager
from src.risk.manager import RiskManager
from src.risk.position_sizer import PositionSizer
from src.portfolio.manager import PortfolioManager
from src.portfolio.tracker import PositionTracker
from src.utils.logging import setup_logging, TradingLogger


class LiveTradingNode:
    """
    Main live trading node for the funding rate arbitrage system.
    """
    
    def __init__(
        self,
        config_path: Optional[str] = None,
        environment: str = "development",
    ) -> None:
        # Load configuration
        if config_path:
            self._config = Config.from_yaml(Path(config_path))
        else:
            self._config = Config()
        
        self._environment = environment
        
        # Core components
        self._trading_node: Optional[TradingNode] = None
        self._analysis_engine: Optional[FundingRateAnalysisEngine] = None
        self._risk_manager: Optional[RiskManager] = None
        self._portfolio_manager: Optional[PortfolioManager] = None
        self._position_tracker: Optional[PositionTracker] = None
        self._market_data_manager: Optional[MarketDataManager] = None
        self._funding_rate_provider: Optional[FundingRateProvider] = None
        
        # State
        self._is_running = False
        self._strategies: Dict[str, FundingRateArbitrageStrategy] = {}
        
        # Logger
        self._logger = TradingLogger("live_trading_node")
        
        # Setup logging
        setup_logging(
            level=self._config.logging.level,
            log_file=self._config.logging.file_path,
            max_file_size=self._config.logging.max_file_size,
            backup_count=self._config.logging.backup_count,
        )
    
    async def start(self) -> None:
        """Start the live trading node."""
        if self._is_running:
            self._logger.logger.warning("Trading node is already running")
            return
        
        self._logger.logger.info(
            f"Starting live trading node in {self._environment} mode",
            trader_id=self._config.trader_id,
            instance_id=self._config.instance_id,
        )
        
        try:
            # Initialize components
            await self._initialize_components()
            
            # Start trading node
            await self._start_trading_node()
            
            # Start analysis engine
            if self._analysis_engine:
                await self._analysis_engine.start()
            
            # Start market data manager
            if self._market_data_manager:
                await self._market_data_manager.start()
            
            self._is_running = True
            
            self._logger.logger.info("Live trading node started successfully")
            
        except Exception as e:
            self._logger.logger.error(f"Failed to start trading node: {e}")
            await self.stop()
            raise
    
    async def stop(self) -> None:
        """Stop the live trading node."""
        if not self._is_running:
            return
        
        self._logger.logger.info("Stopping live trading node")
        
        try:
            # Stop analysis engine
            if self._analysis_engine:
                await self._analysis_engine.stop()
            
            # Stop market data manager
            if self._market_data_manager:
                await self._market_data_manager.stop()
            
            # Stop trading node
            if self._trading_node:
                await self._trading_node.stop()
            
            self._is_running = False
            
            self._logger.logger.info("Live trading node stopped")
            
        except Exception as e:
            self._logger.logger.error(f"Error stopping trading node: {e}")
    
    async def _initialize_components(self) -> None:
        """Initialize all trading components."""
        # Create trading node configuration
        node_config = self._create_trading_node_config()
        
        # Create trading node
        self._trading_node = TradingNode(config=node_config)
        
        # Initialize risk manager
        self._risk_manager = RiskManager(
            portfolio=self._trading_node.portfolio,
            max_position_size=self._config.risk.max_position_size,
            max_portfolio_exposure=self._config.risk.max_portfolio_exposure,
            max_leverage=self._config.risk.max_leverage,
            stop_loss_percentage=self._config.risk.stop_loss_percentage,
        )
        
        # Initialize portfolio manager
        self._portfolio_manager = PortfolioManager(
            portfolio=self._trading_node.portfolio,
        )
        
        # Initialize position tracker
        self._position_tracker = PositionTracker()
        
        # Initialize position sizer
        position_sizer = PositionSizer(
            portfolio=self._trading_node.portfolio,
            default_size=self._config.risk.position_size_percentage,
            max_position_size=self._config.risk.max_position_size,
        )
        
        # Create strategies for each instrument
        for symbol in self._config.strategy.instruments:
            instrument_id = InstrumentId.from_str(f"{symbol}.BINANCE")
            
            strategy_config = FundingRateArbitrageConfig(
                instrument_id=instrument_id,
                max_position_size=self._config.risk.max_position_size,
                min_funding_rate_threshold=self._config.risk.min_funding_rate_threshold,
                min_arbitrage_profit=self._config.strategy.min_arbitrage_profit,
                max_spread_threshold=self._config.strategy.rebalance_threshold,
                position_size_percentage=self._config.risk.position_size_percentage,
                max_portfolio_exposure=self._config.risk.max_portfolio_exposure,
                stop_loss_percentage=self._config.risk.stop_loss_percentage,
                max_leverage=self._config.risk.max_leverage,
                min_time_to_funding=1800,  # 30 minutes
                max_holding_period=self._config.strategy.max_holding_period,
                funding_rate_check_interval=self._config.strategy.funding_rate_check_interval,
            )
            
            strategy = FundingRateArbitrageStrategy(config=strategy_config)
            self._strategies[symbol] = strategy
            
            # Add strategy to trading node
            self._trading_node.trader.add_strategy(strategy)
        
        self._logger.logger.info(
            f"Initialized {len(self._strategies)} strategies",
            instruments=self._config.strategy.instruments,
        )
    
    def _create_trading_node_config(self) -> TradingNodeConfig:
        """Create trading node configuration."""
        # Get Binance configuration
        binance_config = self._config.get_nautilus_config()
        
        # Create data client config
        data_client_config = BinanceDataClientConfig(
            api_key=self._config.binance.api_key,
            api_secret=self._config.binance.api_secret,
            testnet=self._config.binance.testnet,
            us=self._config.binance.us,
        )
        
        # Create execution client config
        exec_client_config = BinanceExecClientConfig(
            api_key=self._config.binance.api_key,
            api_secret=self._config.binance.api_secret,
            testnet=self._config.binance.testnet,
            us=self._config.binance.us,
        )
        
        # Create trading node config
        config = TradingNodeConfig(
            trader_id=self._config.trader_id,
            instance_id=self._config.instance_id,
            data_clients={
                "BINANCE": data_client_config,
            },
            exec_clients={
                "BINANCE": exec_client_config,
            },
            timeout_connection=30.0,
            timeout_reconciliation=10.0,
            timeout_portfolio=10.0,
            timeout_disconnection=10.0,
        )
        
        return config
    
    async def add_instrument(self, symbol: str) -> None:
        """Add a new instrument for trading."""
        if symbol in self._strategies:
            self._logger.logger.warning(f"Instrument {symbol} already exists")
            return
        
        if not self._is_running:
            self._logger.logger.error("Cannot add instrument when node is not running")
            return
        
        try:
            # Create strategy for new instrument
            instrument_id = InstrumentId.from_str(f"{symbol}.BINANCE")
            
            strategy_config = FundingRateArbitrageConfig(
                instrument_id=instrument_id,
                max_position_size=self._config.risk.max_position_size,
                min_funding_rate_threshold=self._config.risk.min_funding_rate_threshold,
                min_arbitrage_profit=self._config.strategy.min_arbitrage_profit,
            )
            
            strategy = FundingRateArbitrageStrategy(config=strategy_config)
            self._strategies[symbol] = strategy
            
            # Add to trading node
            self._trading_node.trader.add_strategy(strategy)
            
            self._logger.logger.info(f"Added instrument {symbol}")
            
        except Exception as e:
            self._logger.logger.error(f"Failed to add instrument {symbol}: {e}")
    
    async def remove_instrument(self, symbol: str) -> None:
        """Remove an instrument from trading."""
        if symbol not in self._strategies:
            self._logger.logger.warning(f"Instrument {symbol} not found")
            return
        
        try:
            # Stop strategy
            strategy = self._strategies[symbol]
            strategy.stop()
            
            # Remove from trading node
            self._trading_node.trader.remove_strategy(strategy)
            
            # Remove from strategies
            del self._strategies[symbol]
            
            self._logger.logger.info(f"Removed instrument {symbol}")
            
        except Exception as e:
            self._logger.logger.error(f"Failed to remove instrument {symbol}: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current system status."""
        status = {
            "is_running": self._is_running,
            "environment": self._environment,
            "trader_id": self._config.trader_id,
            "instance_id": self._config.instance_id,
            "strategies": list(self._strategies.keys()),
            "timestamp": datetime.now(timezone.utc),
        }
        
        if self._trading_node:
            status["portfolio_value"] = float(
                self._trading_node.portfolio.balance_total().as_decimal()
            ) if self._trading_node.portfolio.balance_total() else 0.0
        
        if self._risk_manager:
            risk_summary = self._risk_manager.get_risk_summary()
            status["risk"] = risk_summary
        
        if self._portfolio_manager:
            portfolio_summary = self._portfolio_manager.get_portfolio_summary()
            status["portfolio"] = portfolio_summary
        
        return status
    
    async def emergency_stop(self) -> None:
        """Emergency stop all trading activities."""
        self._logger.logger.critical("EMERGENCY STOP TRIGGERED")
        
        try:
            # Stop all strategies
            for strategy in self._strategies.values():
                strategy.stop()
            
            # Cancel all open orders
            if self._trading_node:
                for order in self._trading_node.cache.orders_open():
                    self._trading_node.trader.cancel_order(order)
            
            # Close all positions (if configured to do so)
            # This is commented out for safety - manual intervention may be preferred
            # if self._trading_node:
            #     for position in self._trading_node.cache.positions_open():
            #         # Close position logic here
            #         pass
            
            self._logger.logger.critical("Emergency stop completed")
            
        except Exception as e:
            self._logger.logger.critical(f"Error during emergency stop: {e}")
    
    async def _start_trading_node(self) -> None:
        """Start the NautilusTrader trading node."""
        if not self._trading_node:
            raise RuntimeError("Trading node not initialized")
        
        # Build the trading node
        self._trading_node.build()
        
        # Start the trading node
        await self._trading_node.start()
        
        self._logger.logger.info("NautilusTrader node started")

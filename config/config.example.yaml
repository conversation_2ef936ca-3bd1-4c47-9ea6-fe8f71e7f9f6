# Funding Rate Arbitrage System Configuration

# Environment settings
environment: "development"  # development, staging, production
debug: true

# System identification
trader_id: "FUNDING_ARBITRAGE_001"
instance_id: "001"

# Binance API configuration
binance:
  api_key: "your_binance_api_key_here"
  api_secret: "your_binance_api_secret_here"
  testnet: true  # Set to false for live trading
  base_url_http: null  # Optional custom endpoint
  base_url_ws: null    # Optional custom endpoint
  us: false            # Set to true for Binance US

# Risk management settings
risk:
  max_position_size: 1000.0        # Maximum position size in USDT
  max_portfolio_exposure: 10000.0  # Maximum total portfolio exposure
  min_funding_rate_threshold: 0.01 # Minimum funding rate (1%) to trigger arbitrage
  max_leverage: 3.0                # Maximum leverage to use
  stop_loss_percentage: 0.05       # Stop loss at 5% adverse move
  position_size_percentage: 0.1    # Use 10% of portfolio per trade

# Trading strategy configuration
strategy:
  funding_rate_check_interval: 300  # Check funding rates every 5 minutes
  min_arbitrage_profit: 0.005       # Minimum 0.5% profit to execute trade
  max_holding_period: 28800         # Maximum 8 hours holding period
  rebalance_threshold: 0.02         # Rebalance when 2% deviation
  instruments:                      # Instruments to trade
    - "BTCUSDT"
    - "ETHUSDT"
    - "ADAUSDT"
    - "BNBUSDT"
    - "SOLUSDT"

# Data configuration
data:
  catalog_path: "./data/catalog"     # Path to data catalog
  historical_data_days: 30          # Days of historical data to maintain
  bar_types:                        # Bar types to collect
    - "1-MINUTE-LAST"
    - "5-MINUTE-LAST"
    - "15-MINUTE-LAST"
  save_raw_data: true               # Save raw market data

# Logging configuration
logging:
  level: "INFO"                     # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "./logs/funding_arbitrage.log"
  max_file_size: "10MB"
  backup_count: 5

# Additional settings for different environments
environments:
  development:
    binance:
      testnet: true
    risk:
      max_position_size: 100.0
      max_portfolio_exposure: 1000.0
    logging:
      level: "DEBUG"
  
  staging:
    binance:
      testnet: true
    risk:
      max_position_size: 500.0
      max_portfolio_exposure: 5000.0
    logging:
      level: "INFO"
  
  production:
    binance:
      testnet: false
    risk:
      max_position_size: 1000.0
      max_portfolio_exposure: 10000.0
    logging:
      level: "WARNING"

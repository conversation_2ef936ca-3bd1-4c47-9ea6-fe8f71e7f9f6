# Environment Configuration for Funding Rate Arbitrage System

# Environment
ENVIRONMENT=development
DEBUG=true

# Binance API Credentials
BINANCE__API_KEY=your_binance_api_key_here
BINANCE__API_SECRET=your_binance_api_secret_here
BINANCE__TESTNET=true
BINANCE__US=false

# Binance Futures API Credentials (if different)
BINANCE_FUTURES_API_KEY=your_binance_futures_api_key_here
BINANCE_FUTURES_API_SECRET=your_binance_futures_api_secret_here
BINANCE_FUTURES_TESTNET_API_KEY=your_binance_futures_testnet_api_key_here
BINANCE_FUTURES_TESTNET_API_SECRET=your_binance_futures_testnet_api_secret_here

# System Settings
TRADER_ID=FUNDING_ARBITRAGE_001
INSTANCE_ID=001

# Risk Management
RISK__MAX_POSITION_SIZE=1000.0
RISK__MAX_PORTFOLIO_EXPOSURE=10000.0
RISK__MIN_FUNDING_RATE_THRESHOLD=0.01
RISK__MAX_LEVERAGE=3.0
RISK__STOP_LOSS_PERCENTAGE=0.05
RISK__POSITION_SIZE_PERCENTAGE=0.1

# Strategy Settings
STRATEGY__FUNDING_RATE_CHECK_INTERVAL=300
STRATEGY__MIN_ARBITRAGE_PROFIT=0.005
STRATEGY__MAX_HOLDING_PERIOD=28800
STRATEGY__REBALANCE_THRESHOLD=0.02

# Data Settings
DATA__CATALOG_PATH=./data/catalog
DATA__HISTORICAL_DATA_DAYS=30
DATA__SAVE_RAW_DATA=true

# Logging
LOGGING__LEVEL=INFO
LOGGING__FILE_PATH=./logs/funding_arbitrage.log
LOGGING__MAX_FILE_SIZE=10MB
LOGGING__BACKUP_COUNT=5

# Database (if using)
DATABASE_URL=sqlite:///./data/funding_arbitrage.db

# Redis (if using for caching)
REDIS_URL=redis://localhost:6379/0

# Monitoring (if using Prometheus)
PROMETHEUS_PORT=8000
PROMETHEUS_HOST=0.0.0.0

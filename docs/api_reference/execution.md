# Execution

```{eval-rst}
.. automodule:: nautilus_trader.execution
```

## Components

```{eval-rst}
.. automodule:: nautilus_trader.execution.algorithm
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.execution.client
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.execution.emulator
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.execution.engine
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.execution.manager
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.execution.matching_core
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Messages

```{eval-rst}
.. automodule:: nautilus_trader.execution.messages
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Reports

```{eval-rst}
.. automodule:: nautilus_trader.execution.reports
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

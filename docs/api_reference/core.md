# Core

```{eval-rst}
.. automodule:: nautilus_trader.core
```

## Datetime

```{eval-rst}
.. automodule:: nautilus_trader.core.datetime
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Finite-State Machine (FSM)

```{eval-rst}
.. automodule:: nautilus_trader.core.fsm
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Message

```{eval-rst}
.. automodule:: nautilus_trader.core.message
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Stats

```{eval-rst}
.. automodule:: nautilus_trader.core.stats
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## UUID

```{eval-rst}
.. automodule:: nautilus_trader.core.uuid
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

# Config

## Backtest

```{eval-rst}
.. automodule:: nautilus_trader.backtest.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Cache

```{eval-rst}
.. automodule:: nautilus_trader.cache.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Common

```{eval-rst}
.. automodule:: nautilus_trader.common.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data

```{eval-rst}
.. automodule:: nautilus_trader.data.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Execution

```{eval-rst}
.. automodule:: nautilus_trader.execution.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Live

```{eval-rst}
.. automodule:: nautilus_trader.live.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Persistence

```{eval-rst}
.. automodule:: nautilus_trader.persistence.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Risk

```{eval-rst}
.. automodule:: nautilus_trader.risk.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## System

```{eval-rst}
.. automodule:: nautilus_trader.system.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Trading

```{eval-rst}
.. automodule:: nautilus_trader.trading.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

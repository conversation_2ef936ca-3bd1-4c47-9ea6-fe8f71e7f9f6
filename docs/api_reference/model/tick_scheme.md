# Tick Scheme

```{eval-rst}
.. automodule:: nautilus_trader.model.tick_scheme
```

```{eval-rst}
.. automodule:: nautilus_trader.model.tick_scheme.implementations.fixed
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.tick_scheme.implementations.tiered
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

```{eval-rst}
.. automodule:: nautilus_trader.model.tick_scheme.base
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

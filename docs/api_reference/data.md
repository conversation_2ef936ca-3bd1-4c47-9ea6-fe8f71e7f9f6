# Data

```{eval-rst}
.. automodule:: nautilus_trader.data
```

## Aggregation

```{eval-rst}
.. automodule:: nautilus_trader.data.aggregation
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Client

```{eval-rst}
.. automodule:: nautilus_trader.data.client
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Engine

```{eval-rst}
.. automodule:: nautilus_trader.data.engine
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Messages

```{eval-rst}
.. automodule:: nautilus_trader.data.messages
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

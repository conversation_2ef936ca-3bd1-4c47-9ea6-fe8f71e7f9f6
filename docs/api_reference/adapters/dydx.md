# dYdX

```{eval-rst}
.. automodule:: nautilus_trader.adapters.dydx
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Config

```{eval-rst}
.. automodule:: nautilus_trader.adapters.dydx.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Factories

```{eval-rst}
.. automodule:: nautilus_trader.adapters.dydx.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Enums

```{eval-rst}
.. automodule:: nautilus_trader.adapters.dydx.common.enums
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.dydx.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.dydx.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Execution

```{eval-rst}
.. automodule:: nautilus_trader.adapters.dydx.execution
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

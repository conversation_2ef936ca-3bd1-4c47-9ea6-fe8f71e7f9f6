# Coinbase International

```{eval-rst}
.. automodule:: nautilus_trader.adapters.coinbase_intx
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Config

```{eval-rst}
.. automodule:: nautilus_trader.adapters.coinbase_intx.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Constants

```{eval-rst}
.. automodule:: nautilus_trader.adapters.coinbase_intx.constants
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Factories

```{eval-rst}
.. automodule:: nautilus_trader.adapters.coinbase_intx.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.coinbase_intx.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.coinbase_intx.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Execution

```{eval-rst}
.. automodule:: nautilus_trader.adapters.coinbase_intx.execution
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

# Betfair

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Client

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.client
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Common

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.common
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Config

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data Types

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.data_types
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Execution

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.execution
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Factories

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## OrderBook

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.orderbook
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Sockets

```{eval-rst}
.. automodule:: nautilus_trader.adapters.betfair.sockets
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

# Tardis

```{eval-rst}
.. automodule:: nautilus_trader.adapters.tardis
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Loaders

```{eval-rst}
.. automodule:: nautilus_trader.adapters.tardis.loaders
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Config

```{eval-rst}
.. automodule:: nautilus_trader.adapters.tardis.config
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Providers

```{eval-rst}
.. automodule:: nautilus_trader.adapters.tardis.providers
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Factories

```{eval-rst}
.. automodule:: nautilus_trader.adapters.tardis.factories
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

## Data

```{eval-rst}
.. automodule:: nautilus_trader.adapters.tardis.data
   :show-inheritance:
   :inherited-members:
   :members:
   :member-order: bysource
```

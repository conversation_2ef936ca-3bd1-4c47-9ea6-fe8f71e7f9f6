# Makefile for Funding Rate Arbitrage System

.PHONY: help setup install test lint format type-check clean run-backtest run-paper run-live

# Default target
help:
	@echo "Available commands:"
	@echo "  setup          - Initial project setup"
	@echo "  install        - Install dependencies"
	@echo "  test           - Run all tests"
	@echo "  test-unit      - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  lint           - Run linting checks"
	@echo "  format         - Format code with black"
	@echo "  type-check     - Run type checking with mypy"
	@echo "  clean          - Clean up temporary files"
	@echo "  run-backtest   - Run backtesting"
	@echo "  run-paper      - Run paper trading"
	@echo "  run-live       - Run live trading"
	@echo "  validate-config - Validate configuration"
	@echo "  status         - Show system status"

# Setup and installation
setup:
	python scripts/setup.py

install:
	pip install --upgrade pip
	pip install -r requirements.txt

install-dev:
	pip install --upgrade pip
	pip install -r requirements.txt
	pip install -e ".[dev,research,monitoring]"

# Testing
test:
	pytest tests/ -v --cov=src --cov-report=term-missing --cov-report=html

test-unit:
	pytest tests/unit/ -v

test-integration:
	pytest tests/integration/ -v

test-slow:
	pytest tests/ -v -m slow

# Code quality
lint:
	flake8 src/ tests/
	black src/ tests/ --check

format:
	black src/ tests/
	isort src/ tests/

type-check:
	mypy src/

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# Running the system
run-backtest:
	python -m src.cli backtest

run-paper:
	python -m src.cli paper-trade

run-live:
	python -m src.cli live-trade

# Configuration and status
validate-config:
	python -m src.cli validate-config

status:
	python -m src.cli status

export-config:
	python -m src.cli export-config

# Development helpers
jupyter:
	jupyter lab notebooks/

docs:
	@echo "Documentation generation not yet implemented"

# Docker (future)
docker-build:
	@echo "Docker support not yet implemented"

docker-run:
	@echo "Docker support not yet implemented"

# Monitoring
monitor:
	@echo "Monitoring dashboard not yet implemented"

# Data management
download-data:
	@echo "Data download script not yet implemented"

backup-data:
	@echo "Data backup script not yet implemented"

"""
Unit tests for configuration management.
"""

import pytest
from decimal import Decimal
from pathlib import Path

from src.config import Config, BinanceConfig, RiskConfig, StrategyConfig


class TestConfig:
    """Test configuration management."""
    
    def test_default_config(self):
        """Test default configuration creation."""
        config = Config()
        
        assert config.environment == "development"
        assert config.debug is True
        assert config.trader_id == "FUNDING_ARBITRAGE_001"
        assert config.instance_id == "001"
    
    def test_binance_config(self):
        """Test Binance configuration."""
        binance_config = BinanceConfig(
            api_key="test_key",
            api_secret="test_secret",
            testnet=True,
        )
        
        assert binance_config.api_key == "test_key"
        assert binance_config.api_secret == "test_secret"
        assert binance_config.testnet is True
        assert binance_config.us is False
    
    def test_risk_config(self):
        """Test risk configuration."""
        risk_config = RiskConfig(
            max_position_size=Decimal("500.0"),
            min_funding_rate_threshold=Decimal("0.005"),
        )
        
        assert risk_config.max_position_size == Decimal("500.0")
        assert risk_config.min_funding_rate_threshold == Decimal("0.005")
        assert risk_config.max_leverage == Decimal("3.0")  # default
    
    def test_strategy_config(self):
        """Test strategy configuration."""
        strategy_config = StrategyConfig(
            instruments=["BTCUSDT", "ETHUSDT"],
            funding_rate_check_interval=120,
        )
        
        assert strategy_config.instruments == ["BTCUSDT", "ETHUSDT"]
        assert strategy_config.funding_rate_check_interval == 120
        assert strategy_config.min_arbitrage_profit == Decimal("0.005")  # default
    
    def test_nautilus_config_generation(self):
        """Test NautilusTrader configuration generation."""
        config = Config(
            trader_id="TEST_TRADER",
            instance_id="TEST_001",
            binance=BinanceConfig(
                api_key="test_key",
                api_secret="test_secret",
                testnet=True,
            ),
        )
        
        nautilus_config = config.get_nautilus_config()
        
        assert nautilus_config["trader_id"] == "TEST_TRADER"
        assert nautilus_config["instance_id"] == "TEST_001"
        assert "data_clients" in nautilus_config
        assert "exec_clients" in nautilus_config
        assert nautilus_config["data_clients"]["BINANCE"]["api_key"] == "test_key"
        assert nautilus_config["data_clients"]["BINANCE"]["testnet"] is True
    
    def test_yaml_serialization(self, tmp_path):
        """Test YAML serialization and deserialization."""
        config = Config(
            environment="test",
            trader_id="TEST_TRADER",
            binance=BinanceConfig(
                api_key="test_key",
                api_secret="test_secret",
            ),
        )
        
        yaml_path = tmp_path / "test_config.yaml"
        config.to_yaml(yaml_path)
        
        assert yaml_path.exists()
        
        loaded_config = Config.from_yaml(yaml_path)
        assert loaded_config.environment == "test"
        assert loaded_config.trader_id == "TEST_TRADER"
        assert loaded_config.binance.api_key == "test_key"

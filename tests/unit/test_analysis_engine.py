"""
Unit tests for the funding rate analysis engine.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, AsyncMock

from src.strategies.analysis_engine import (
    FundingRateAnalysisEngine,
    ArbitrageSignal,
    SignalType,
)
from src.data.funding_rate import FundingRateData
from src.data.market_data import MarketDataManager
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.data import QuoteTick
from nautilus_trader.model.enums import AggressorSide
from nautilus_trader.model.objects import Price, Quantity


class TestFundingRateAnalysisEngine:
    """Test FundingRateAnalysisEngine class."""
    
    @pytest.fixture
    def mock_market_data_manager(self):
        """Mock market data manager."""
        manager = Mock(spec=MarketDataManager)
        
        # Mock spot quote
        spot_quote = Mock(spec=QuoteTick)
        spot_quote.bid_price = Price.from_str("47000.0")
        spot_quote.ask_price = Price.from_str("47002.0")
        manager.get_spot_quote.return_value = spot_quote
        
        # Mock futures quote
        futures_quote = Mock(spec=QuoteTick)
        futures_quote.bid_price = Price.from_str("47010.0")
        futures_quote.ask_price = Price.from_str("47012.0")
        manager.get_futures_quote.return_value = futures_quote
        
        # Mock funding rate data
        funding_rate_data = FundingRateData(
            instrument_id=InstrumentId.from_str("BTCUSDT-PERP.BINANCE"),
            funding_rate=Decimal("0.002"),  # 0.2%
            funding_time=1640995200000,
            mark_price=Decimal("47011.0"),
        )
        manager.get_funding_rate.return_value = funding_rate_data
        
        return manager
    
    @pytest.fixture
    def mock_funding_rate_provider(self):
        """Mock funding rate provider."""
        provider = Mock()
        provider.get_current_funding_rates = AsyncMock(return_value={})
        return provider
    
    @pytest.fixture
    def analysis_engine(self, mock_market_data_manager, mock_funding_rate_provider):
        """Create analysis engine for testing."""
        return FundingRateAnalysisEngine(
            market_data_manager=mock_market_data_manager,
            funding_rate_provider=mock_funding_rate_provider,
            instruments=["BTCUSDT"],
            min_funding_rate_threshold=Decimal("0.01"),
            min_arbitrage_profit=Decimal("0.005"),
        )
    
    def test_analysis_engine_creation(self, analysis_engine):
        """Test creating analysis engine."""
        assert analysis_engine._instruments == ["BTCUSDT"]
        assert analysis_engine._min_funding_rate_threshold == Decimal("0.01")
        assert analysis_engine._min_arbitrage_profit == Decimal("0.005")
        assert not analysis_engine._analysis_running
    
    def test_add_signal_callback(self, analysis_engine):
        """Test adding signal callback."""
        callback = Mock()
        analysis_engine.add_signal_callback(callback)
        
        assert callback in analysis_engine._signal_callbacks
    
    @pytest.mark.asyncio
    async def test_start_stop(self, analysis_engine):
        """Test starting and stopping analysis engine."""
        assert not analysis_engine._analysis_running
        
        await analysis_engine.start()
        assert analysis_engine._analysis_running
        
        await analysis_engine.stop()
        assert not analysis_engine._analysis_running
    
    @pytest.mark.asyncio
    async def test_analyze_instrument_positive_funding(self, analysis_engine, mock_market_data_manager):
        """Test analyzing instrument with positive funding rate."""
        # Set up positive funding rate (should trigger long arbitrage)
        funding_rate_data = FundingRateData(
            instrument_id=InstrumentId.from_str("BTCUSDT-PERP.BINANCE"),
            funding_rate=Decimal("0.015"),  # 1.5% - above threshold
            funding_time=1640995200000,
            mark_price=Decimal("47011.0"),
        )
        mock_market_data_manager.get_funding_rate.return_value = funding_rate_data
        
        signal = await analysis_engine._analyze_instrument("BTCUSDT")
        
        assert signal is not None
        assert signal.symbol == "BTCUSDT"
        assert signal.signal_type == SignalType.LONG_ARBITRAGE
        assert signal.funding_rate == Decimal("0.015")
        assert signal.expected_profit > Decimal("0")
        assert signal.confidence > Decimal("0")
    
    @pytest.mark.asyncio
    async def test_analyze_instrument_negative_funding(self, analysis_engine, mock_market_data_manager):
        """Test analyzing instrument with negative funding rate."""
        # Set up negative funding rate (should trigger short arbitrage)
        funding_rate_data = FundingRateData(
            instrument_id=InstrumentId.from_str("BTCUSDT-PERP.BINANCE"),
            funding_rate=Decimal("-0.015"),  # -1.5% - above threshold
            funding_time=1640995200000,
            mark_price=Decimal("47011.0"),
        )
        mock_market_data_manager.get_funding_rate.return_value = funding_rate_data
        
        signal = await analysis_engine._analyze_instrument("BTCUSDT")
        
        assert signal is not None
        assert signal.symbol == "BTCUSDT"
        assert signal.signal_type == SignalType.SHORT_ARBITRAGE
        assert signal.funding_rate == Decimal("-0.015")
        assert signal.expected_profit > Decimal("0")
        assert signal.confidence > Decimal("0")
    
    @pytest.mark.asyncio
    async def test_analyze_instrument_low_funding(self, analysis_engine, mock_market_data_manager):
        """Test analyzing instrument with low funding rate."""
        # Set up low funding rate (should not trigger arbitrage)
        funding_rate_data = FundingRateData(
            instrument_id=InstrumentId.from_str("BTCUSDT-PERP.BINANCE"),
            funding_rate=Decimal("0.005"),  # 0.5% - below threshold
            funding_time=1640995200000,
            mark_price=Decimal("47011.0"),
        )
        mock_market_data_manager.get_funding_rate.return_value = funding_rate_data
        
        signal = await analysis_engine._analyze_instrument("BTCUSDT")
        
        assert signal is not None
        assert signal.symbol == "BTCUSDT"
        assert signal.signal_type == SignalType.NO_SIGNAL
        assert signal.expected_profit == Decimal("0")
        assert signal.confidence == Decimal("0")
    
    @pytest.mark.asyncio
    async def test_analyze_instrument_missing_data(self, analysis_engine, mock_market_data_manager):
        """Test analyzing instrument with missing market data."""
        # Remove market data
        mock_market_data_manager.get_spot_quote.return_value = None
        
        signal = await analysis_engine._analyze_instrument("BTCUSDT")
        
        assert signal is None
    
    @pytest.mark.asyncio
    async def test_analyze_instrument_wide_spread(self, analysis_engine, mock_market_data_manager):
        """Test analyzing instrument with wide spread."""
        # Set up wide spread (should not trigger arbitrage)
        spot_quote = Mock(spec=QuoteTick)
        spot_quote.bid_price = Price.from_str("47000.0")
        spot_quote.ask_price = Price.from_str("47002.0")
        mock_market_data_manager.get_spot_quote.return_value = spot_quote
        
        futures_quote = Mock(spec=QuoteTick)
        futures_quote.bid_price = Price.from_str("48000.0")  # Very wide spread
        futures_quote.ask_price = Price.from_str("48002.0")
        mock_market_data_manager.get_futures_quote.return_value = futures_quote
        
        # High funding rate but wide spread
        funding_rate_data = FundingRateData(
            instrument_id=InstrumentId.from_str("BTCUSDT-PERP.BINANCE"),
            funding_rate=Decimal("0.015"),
            funding_time=1640995200000,
            mark_price=Decimal("48001.0"),
        )
        mock_market_data_manager.get_funding_rate.return_value = funding_rate_data
        
        signal = await analysis_engine._analyze_instrument("BTCUSDT")
        
        assert signal is not None
        assert signal.signal_type == SignalType.NO_SIGNAL  # Wide spread should prevent arbitrage
    
    def test_evaluate_arbitrage_opportunity_positive_funding(self, analysis_engine):
        """Test evaluating arbitrage opportunity with positive funding."""
        signal_type, expected_profit, confidence = analysis_engine._evaluate_arbitrage_opportunity(
            symbol="BTCUSDT",
            funding_rate=Decimal("0.015"),  # 1.5%
            spot_price=Decimal("47001.0"),
            futures_price=Decimal("47011.0"),
            spread_percentage=Decimal("0.021"),  # 0.021%
        )
        
        assert signal_type == SignalType.LONG_ARBITRAGE
        assert expected_profit > Decimal("0")
        assert confidence > Decimal("0")
    
    def test_evaluate_arbitrage_opportunity_negative_funding(self, analysis_engine):
        """Test evaluating arbitrage opportunity with negative funding."""
        signal_type, expected_profit, confidence = analysis_engine._evaluate_arbitrage_opportunity(
            symbol="BTCUSDT",
            funding_rate=Decimal("-0.015"),  # -1.5%
            spot_price=Decimal("47001.0"),
            futures_price=Decimal("47011.0"),
            spread_percentage=Decimal("0.021"),  # 0.021%
        )
        
        assert signal_type == SignalType.SHORT_ARBITRAGE
        assert expected_profit > Decimal("0")
        assert confidence > Decimal("0")
    
    def test_get_current_signal(self, analysis_engine):
        """Test getting current signal."""
        # No signal initially
        assert analysis_engine.get_current_signal("BTCUSDT") is None
        
        # Add a signal
        signal = ArbitrageSignal(
            symbol="BTCUSDT",
            signal_type=SignalType.LONG_ARBITRAGE,
            funding_rate=Decimal("0.015"),
            spot_price=Decimal("47001.0"),
            futures_price=Decimal("47011.0"),
            spread=Decimal("0.021"),
            expected_profit=Decimal("0.01"),
            confidence=Decimal("0.8"),
            timestamp=datetime.now(timezone.utc),
            next_funding_time=datetime.now(timezone.utc) + timedelta(hours=8),
            time_to_funding=timedelta(hours=8),
        )
        analysis_engine._current_signals["BTCUSDT"] = signal
        
        retrieved_signal = analysis_engine.get_current_signal("BTCUSDT")
        assert retrieved_signal == signal
    
    def test_get_signal_history(self, analysis_engine):
        """Test getting signal history."""
        # No history initially
        assert analysis_engine.get_signal_history() == []
        
        # Add signals to history
        signal1 = ArbitrageSignal(
            symbol="BTCUSDT",
            signal_type=SignalType.LONG_ARBITRAGE,
            funding_rate=Decimal("0.015"),
            spot_price=Decimal("47001.0"),
            futures_price=Decimal("47011.0"),
            spread=Decimal("0.021"),
            expected_profit=Decimal("0.01"),
            confidence=Decimal("0.8"),
            timestamp=datetime.now(timezone.utc),
            next_funding_time=datetime.now(timezone.utc) + timedelta(hours=8),
            time_to_funding=timedelta(hours=8),
        )
        
        signal2 = ArbitrageSignal(
            symbol="ETHUSDT",
            signal_type=SignalType.SHORT_ARBITRAGE,
            funding_rate=Decimal("-0.012"),
            spot_price=Decimal("3501.0"),
            futures_price=Decimal("3505.0"),
            spread=Decimal("0.11"),
            expected_profit=Decimal("0.008"),
            confidence=Decimal("0.7"),
            timestamp=datetime.now(timezone.utc),
            next_funding_time=datetime.now(timezone.utc) + timedelta(hours=8),
            time_to_funding=timedelta(hours=8),
        )
        
        analysis_engine._signal_history = [signal1, signal2]
        
        # Test getting all history
        history = analysis_engine.get_signal_history()
        assert len(history) == 2
        assert signal1 in history
        assert signal2 in history
        
        # Test filtering by symbol
        btc_history = analysis_engine.get_signal_history(symbol="BTCUSDT")
        assert len(btc_history) == 1
        assert btc_history[0] == signal1
        
        # Test limiting results
        limited_history = analysis_engine.get_signal_history(limit=1)
        assert len(limited_history) == 1
        assert limited_history[0] == signal2  # Should get the last one
    
    def test_get_analysis_summary(self, analysis_engine):
        """Test getting analysis summary."""
        # Add some test data
        signal = ArbitrageSignal(
            symbol="BTCUSDT",
            signal_type=SignalType.LONG_ARBITRAGE,
            funding_rate=Decimal("0.015"),
            spot_price=Decimal("47001.0"),
            futures_price=Decimal("47011.0"),
            spread=Decimal("0.021"),
            expected_profit=Decimal("0.01"),
            confidence=Decimal("0.8"),
            timestamp=datetime.now(timezone.utc),
            next_funding_time=datetime.now(timezone.utc) + timedelta(hours=8),
            time_to_funding=timedelta(hours=8),
        )
        
        analysis_engine._current_signals["BTCUSDT"] = signal
        analysis_engine._signal_history = [signal]
        
        summary = analysis_engine.get_analysis_summary()
        
        assert "timestamp" in summary
        assert summary["analysis_running"] == False
        assert summary["instruments_analyzed"] == 1
        assert summary["active_signals"] == 1
        assert summary["total_signals_generated"] == 1
        assert "current_signals" in summary
        assert "BTCUSDT" in summary["current_signals"]

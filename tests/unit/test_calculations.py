"""
Unit tests for calculation utilities.
"""

import pytest
from decimal import Decimal

import pandas as pd

from src.utils.calculations import (
    calculate_funding_rate_profit,
    calculate_hedge_ratio,
    calculate_position_size,
    calculate_arbitrage_profit,
    calculate_break_even_funding_rate,
    calculate_sharpe_ratio,
    calculate_max_drawdown,
)


class TestCalculations:
    """Test calculation utilities."""
    
    def test_calculate_funding_rate_profit(self):
        """Test funding rate profit calculation."""
        funding_rate = Decimal("0.0001")  # 0.01%
        position_size = Decimal("1.0")
        mark_price = Decimal("47000.0")
        
        profit = calculate_funding_rate_profit(
            funding_rate=funding_rate,
            position_size=position_size,
            mark_price=mark_price,
            holding_periods=1,
        )
        
        expected_profit = Decimal("1.0") * Decimal("47000.0") * Decimal("0.0001") * Decimal("1")
        assert profit == expected_profit
        assert profit == Decimal("4.7")
    
    def test_calculate_funding_rate_profit_multiple_periods(self):
        """Test funding rate profit calculation for multiple periods."""
        profit = calculate_funding_rate_profit(
            funding_rate=Decimal("0.0001"),
            position_size=Decimal("1.0"),
            mark_price=Decimal("47000.0"),
            holding_periods=3,
        )
        
        expected_profit = Decimal("1.0") * Decimal("47000.0") * Decimal("0.0001") * Decimal("3")
        assert profit == expected_profit
        assert profit == Decimal("14.1")
    
    def test_calculate_hedge_ratio_simple(self):
        """Test simple hedge ratio calculation."""
        spot_price = Decimal("47000.0")
        futures_price = Decimal("47100.0")
        
        hedge_ratio = calculate_hedge_ratio(spot_price, futures_price)
        
        expected_ratio = spot_price / futures_price
        assert hedge_ratio == expected_ratio
        assert abs(hedge_ratio - Decimal("0.997877")) < Decimal("0.000001")
    
    def test_calculate_hedge_ratio_with_volatility(self):
        """Test hedge ratio calculation with volatility parameters."""
        spot_price = Decimal("47000.0")
        futures_price = Decimal("47100.0")
        spot_volatility = Decimal("0.02")
        futures_volatility = Decimal("0.025")
        correlation = Decimal("0.95")
        
        hedge_ratio = calculate_hedge_ratio(
            spot_price=spot_price,
            futures_price=futures_price,
            spot_volatility=spot_volatility,
            futures_volatility=futures_volatility,
            correlation=correlation,
        )
        
        expected_ratio = correlation * (spot_volatility / futures_volatility)
        assert hedge_ratio == expected_ratio
        assert hedge_ratio == Decimal("0.76")
    
    def test_calculate_position_size(self):
        """Test position size calculation."""
        available_capital = Decimal("10000.0")
        risk_percentage = Decimal("0.1")  # 10%
        price = Decimal("47000.0")
        
        position_size = calculate_position_size(
            available_capital=available_capital,
            risk_percentage=risk_percentage,
            price=price,
        )
        
        expected_size = (available_capital * risk_percentage) / price
        assert position_size == expected_size
        assert abs(position_size - Decimal("0.021277")) < Decimal("0.000001")
    
    def test_calculate_position_size_with_leverage(self):
        """Test position size calculation with leverage."""
        position_size = calculate_position_size(
            available_capital=Decimal("10000.0"),
            risk_percentage=Decimal("0.1"),
            price=Decimal("47000.0"),
            leverage=Decimal("3.0"),
        )
        
        expected_size = (Decimal("10000.0") * Decimal("0.1") * Decimal("3.0")) / Decimal("47000.0")
        assert position_size == expected_size
        assert abs(position_size - Decimal("0.063830")) < Decimal("0.000001")
    
    def test_calculate_position_size_with_max_limit(self):
        """Test position size calculation with maximum limit."""
        position_size = calculate_position_size(
            available_capital=Decimal("10000.0"),
            risk_percentage=Decimal("0.5"),  # 50%
            price=Decimal("100.0"),
            max_position_size=Decimal("10.0"),
        )
        
        # Without limit: (10000 * 0.5) / 100 = 50
        # With limit: min(50, 10) = 10
        assert position_size == Decimal("10.0")
    
    def test_calculate_arbitrage_profit(self):
        """Test arbitrage profit calculation."""
        spot_price = Decimal("47000.0")
        futures_price = Decimal("47100.0")
        funding_rate = Decimal("0.0001")
        position_size = Decimal("1.0")
        
        funding_profit, convergence_profit, total_profit = calculate_arbitrage_profit(
            spot_price=spot_price,
            futures_price=futures_price,
            funding_rate=funding_rate,
            position_size=position_size,
        )
        
        # Funding profit: 1.0 * 47100.0 * 0.0001 = 4.71
        assert funding_profit == Decimal("4.71")
        
        # Convergence profit: 1.0 * (47100.0 - 47000.0) = 100.0
        assert convergence_profit == Decimal("100.0")
        
        # Transaction costs: 47100.0 * 0.001 * 2 = 94.2
        # Total profit: 4.71 + 100.0 - 94.2 = 10.51
        assert total_profit == Decimal("10.51")
    
    def test_calculate_break_even_funding_rate(self):
        """Test break-even funding rate calculation."""
        spot_price = Decimal("47000.0")
        futures_price = Decimal("47100.0")
        transaction_costs = Decimal("0.001")
        
        break_even_rate = calculate_break_even_funding_rate(
            spot_price=spot_price,
            futures_price=futures_price,
            transaction_costs=transaction_costs,
        )
        
        # Price spread cost: (47100 - 47000) / 47100 = 0.002123
        # Transaction cost: 0.001 * 2 = 0.002
        # Break-even: (0.002123 + 0.002) / 1 = 0.004123
        expected_rate = (Decimal("100.0") / Decimal("47100.0")) + (Decimal("0.001") * Decimal("2"))
        assert abs(break_even_rate - expected_rate) < Decimal("0.000001")
    
    def test_calculate_sharpe_ratio(self):
        """Test Sharpe ratio calculation."""
        # Create sample returns
        returns = pd.Series([0.01, 0.02, -0.01, 0.015, 0.005] * 50)  # 250 daily returns
        
        sharpe_ratio = calculate_sharpe_ratio(returns, risk_free_rate=Decimal("0.02"))
        
        # Should return a reasonable Sharpe ratio
        assert isinstance(sharpe_ratio, Decimal)
        assert sharpe_ratio > Decimal("-5.0")  # Reasonable bounds
        assert sharpe_ratio < Decimal("5.0")
    
    def test_calculate_sharpe_ratio_empty_series(self):
        """Test Sharpe ratio with empty series."""
        returns = pd.Series([])
        sharpe_ratio = calculate_sharpe_ratio(returns)
        assert sharpe_ratio == Decimal("0")
    
    def test_calculate_sharpe_ratio_zero_volatility(self):
        """Test Sharpe ratio with zero volatility."""
        returns = pd.Series([0.01] * 100)  # Constant returns
        sharpe_ratio = calculate_sharpe_ratio(returns)
        assert sharpe_ratio == Decimal("0")
    
    def test_calculate_max_drawdown(self):
        """Test maximum drawdown calculation."""
        # Create equity curve with a drawdown
        dates = pd.date_range("2022-01-01", periods=100, freq="D")
        equity_values = [1000 + i * 10 for i in range(50)]  # Rising
        equity_values += [1500 - i * 5 for i in range(50)]  # Falling (drawdown)
        
        equity_curve = pd.Series(equity_values, index=dates)
        
        max_drawdown, start_date, end_date = calculate_max_drawdown(equity_curve)
        
        # Should detect the drawdown
        assert max_drawdown > Decimal("0")
        assert start_date is not None
        assert end_date is not None
        assert start_date <= end_date
    
    def test_calculate_max_drawdown_empty_series(self):
        """Test maximum drawdown with empty series."""
        equity_curve = pd.Series([])
        max_drawdown, start_date, end_date = calculate_max_drawdown(equity_curve)
        
        assert max_drawdown == Decimal("0")
        assert start_date is None
        assert end_date is None

"""
Unit tests for the backtesting engine.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timezone, timedelta

from src.backtesting.engine import BacktestEngine
from src.backtesting.results import BacktestResults, TradeResult


class TestBacktestEngine:
    """Test BacktestEngine class."""
    
    def test_engine_creation(self):
        """Test creating backtest engine."""
        engine = BacktestEngine(
            start_capital=Decimal("10000.0"),
            commission_rate=Decimal("0.001"),
            slippage_bps=1,
        )
        
        assert engine._start_capital == Decimal("10000.0")
        assert engine._commission_rate == Decimal("0.001")
        assert engine._slippage_bps == 1
        assert engine._current_capital == Decimal("10000.0")
    
    def test_add_trade_callback(self):
        """Test adding trade callback."""
        engine = BacktestEngine()
        callback = lambda trade: None
        
        engine.add_trade_callback(callback)
        assert callback in engine._trade_callbacks
    
    def test_get_price_at_time(self):
        """Test getting price at specific time."""
        import pandas as pd
        
        engine = BacktestEngine()
        
        # Create test data
        timestamps = pd.date_range("2024-01-01", periods=5, freq="1T")
        data = pd.DataFrame({
            "close": [100.0, 101.0, 102.0, 103.0, 104.0]
        }, index=timestamps)
        
        # Test exact timestamp
        price = engine._get_price_at_time(data, timestamps[2])
        assert price == Decimal("102.0")
        
        # Test nearest timestamp
        test_time = timestamps[2] + timedelta(seconds=30)
        price = engine._get_price_at_time(data, test_time)
        assert price == Decimal("102.0")
    
    def test_get_funding_rate_at_time(self):
        """Test getting funding rate at specific time."""
        import pandas as pd
        
        engine = BacktestEngine()
        
        # Create test funding rate data
        timestamps = pd.date_range("2024-01-01", periods=3, freq="8H")
        data = pd.DataFrame({
            "funding_rate": [0.001, 0.002, 0.003]
        }, index=timestamps)
        
        # Test getting most recent rate
        test_time = timestamps[1] + timedelta(hours=4)
        rate = engine._get_funding_rate_at_time(data, test_time)
        assert rate == Decimal("0.002")
    
    def test_analyze_arbitrage_opportunity_positive_funding(self):
        """Test arbitrage analysis with positive funding rate."""
        engine = BacktestEngine()
        engine._current_time = datetime.now(timezone.utc)
        
        strategy_config = {
            "min_funding_rate_threshold": 0.01,
            "min_arbitrage_profit": 0.005,
            "max_spread_threshold": 0.02,
        }
        
        signal = engine._analyze_arbitrage_opportunity(
            symbol="BTCUSDT",
            spot_price=Decimal("47000.0"),
            futures_price=Decimal("47100.0"),
            funding_rate=Decimal("0.015"),  # 1.5%
            strategy_config=strategy_config,
        )
        
        assert signal is not None
        assert signal.signal_type.value == "long_arbitrage"
        assert signal.funding_rate == Decimal("0.015")
        assert signal.expected_profit == Decimal("0.015")
    
    def test_analyze_arbitrage_opportunity_negative_funding(self):
        """Test arbitrage analysis with negative funding rate."""
        engine = BacktestEngine()
        engine._current_time = datetime.now(timezone.utc)
        
        strategy_config = {
            "min_funding_rate_threshold": 0.01,
            "min_arbitrage_profit": 0.005,
            "max_spread_threshold": 0.02,
        }
        
        signal = engine._analyze_arbitrage_opportunity(
            symbol="BTCUSDT",
            spot_price=Decimal("47000.0"),
            futures_price=Decimal("47100.0"),
            funding_rate=Decimal("-0.015"),  # -1.5%
            strategy_config=strategy_config,
        )
        
        assert signal is not None
        assert signal.signal_type.value == "short_arbitrage"
        assert signal.funding_rate == Decimal("-0.015")
        assert signal.expected_profit == Decimal("0.015")
    
    def test_analyze_arbitrage_opportunity_low_funding(self):
        """Test arbitrage analysis with low funding rate."""
        engine = BacktestEngine()
        engine._current_time = datetime.now(timezone.utc)
        
        strategy_config = {
            "min_funding_rate_threshold": 0.01,
            "min_arbitrage_profit": 0.005,
            "max_spread_threshold": 0.02,
        }
        
        signal = engine._analyze_arbitrage_opportunity(
            symbol="BTCUSDT",
            spot_price=Decimal("47000.0"),
            futures_price=Decimal("47100.0"),
            funding_rate=Decimal("0.003"),  # 0.3% - below threshold
            strategy_config=strategy_config,
        )
        
        assert signal is None
    
    def test_analyze_arbitrage_opportunity_wide_spread(self):
        """Test arbitrage analysis with wide spread."""
        engine = BacktestEngine()
        engine._current_time = datetime.now(timezone.utc)
        
        strategy_config = {
            "min_funding_rate_threshold": 0.01,
            "min_arbitrage_profit": 0.005,
            "max_spread_threshold": 0.02,
        }
        
        signal = engine._analyze_arbitrage_opportunity(
            symbol="BTCUSDT",
            spot_price=Decimal("47000.0"),
            futures_price=Decimal("48500.0"),  # 3.2% spread - too wide
            funding_rate=Decimal("0.015"),
            strategy_config=strategy_config,
        )
        
        assert signal is None
    
    @pytest.mark.asyncio
    async def test_open_position(self):
        """Test opening a position."""
        from src.strategies.analysis_engine import ArbitrageSignal, SignalType
        
        engine = BacktestEngine(start_capital=Decimal("10000.0"))
        engine._current_time = datetime.now(timezone.utc)
        
        # Initialize position
        engine._positions["BTCUSDT"] = {
            "status": "closed",
            "arbitrage_type": None,
            "entry_time": None,
            "entry_funding_rate": None,
            "spot_size": Decimal("0"),
            "futures_size": Decimal("0"),
            "entry_spot_price": None,
            "entry_futures_price": None,
        }
        
        signal = ArbitrageSignal(
            symbol="BTCUSDT",
            signal_type=SignalType.LONG_ARBITRAGE,
            funding_rate=Decimal("0.015"),
            spot_price=Decimal("47000.0"),
            futures_price=Decimal("47100.0"),
            spread=Decimal("0.21"),
            expected_profit=Decimal("0.015"),
            confidence=Decimal("0.8"),
            timestamp=engine._current_time,
            next_funding_time=engine._current_time + timedelta(hours=8),
            time_to_funding=timedelta(hours=8),
        )
        
        await engine._open_position("BTCUSDT", signal, Decimal("47000.0"), Decimal("47100.0"))
        
        position = engine._positions["BTCUSDT"]
        assert position["status"] == "open"
        assert position["arbitrage_type"] == "long"
        assert position["entry_funding_rate"] == Decimal("0.015")
        assert position["spot_size"] > 0
        assert position["futures_size"] < 0
    
    @pytest.mark.asyncio
    async def test_close_position(self):
        """Test closing a position."""
        engine = BacktestEngine(start_capital=Decimal("10000.0"))
        engine._current_time = datetime.now(timezone.utc)
        engine._results = BacktestResults(
            start_capital=Decimal("10000.0"),
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc),
        )
        
        # Set up open position
        entry_time = engine._current_time - timedelta(hours=8)
        engine._positions["BTCUSDT"] = {
            "status": "open",
            "arbitrage_type": "long",
            "entry_time": entry_time,
            "entry_funding_rate": Decimal("0.015"),
            "spot_size": Decimal("0.1"),
            "futures_size": Decimal("-0.1"),
            "entry_spot_price": Decimal("47000.0"),
            "entry_futures_price": Decimal("47100.0"),
        }
        
        await engine._close_position("BTCUSDT", Decimal("47200.0"), Decimal("47300.0"))
        
        position = engine._positions["BTCUSDT"]
        assert position["status"] == "closed"
        assert position["spot_size"] == Decimal("0")
        assert position["futures_size"] == Decimal("0")
        
        # Check that trade was recorded
        trades = engine._results.get_trades_dataframe()
        assert len(trades) == 1
        assert trades.iloc[0]["symbol"] == "BTCUSDT"


class TestBacktestResults:
    """Test BacktestResults class."""
    
    def test_results_creation(self):
        """Test creating backtest results."""
        start_date = datetime(2024, 1, 1, tzinfo=timezone.utc)
        end_date = datetime(2024, 1, 31, tzinfo=timezone.utc)
        
        results = BacktestResults(
            start_capital=Decimal("10000.0"),
            start_date=start_date,
            end_date=end_date,
        )
        
        assert results._start_capital == Decimal("10000.0")
        assert results._start_date == start_date
        assert results._end_date == end_date
        assert len(results._trades) == 0
    
    def test_add_trade(self):
        """Test adding a trade."""
        results = BacktestResults(
            start_capital=Decimal("10000.0"),
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc),
        )
        
        trade = TradeResult(
            trade_id="test_trade_1",
            symbol="BTCUSDT",
            entry_time=datetime.now(timezone.utc),
            exit_time=datetime.now(timezone.utc),
            arbitrage_type="long",
            entry_funding_rate=Decimal("0.015"),
            position_size=Decimal("0.1"),
            entry_spot_price=Decimal("47000.0"),
            entry_futures_price=Decimal("47100.0"),
            exit_spot_price=Decimal("47200.0"),
            exit_futures_price=Decimal("47300.0"),
            trading_pnl=Decimal("10.0"),
            funding_pnl=Decimal("5.0"),
            total_pnl=Decimal("15.0"),
            holding_period_hours=8.0,
            return_percentage=Decimal("0.32"),
            fees_paid=Decimal("2.0"),
        )
        
        results.add_trade(trade)
        
        assert len(results._trades) == 1
        assert results._trades[0] == trade
        assert results._metrics_dirty is True
    
    def test_add_equity_point(self):
        """Test adding equity curve point."""
        results = BacktestResults(
            start_capital=Decimal("10000.0"),
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc),
        )
        
        timestamp = datetime.now(timezone.utc)
        equity = Decimal("10500.0")
        
        results.add_equity_point(timestamp, equity)
        
        assert len(results._equity_curve) == 1
        assert results._equity_curve[0] == (timestamp, equity)
    
    def test_calculate_metrics_empty(self):
        """Test calculating metrics with no trades."""
        results = BacktestResults(
            start_capital=Decimal("10000.0"),
            start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2024, 1, 31, tzinfo=timezone.utc),
        )
        
        # Add some equity points
        results.add_equity_point(datetime(2024, 1, 1, tzinfo=timezone.utc), Decimal("10000.0"))
        results.add_equity_point(datetime(2024, 1, 31, tzinfo=timezone.utc), Decimal("10500.0"))
        
        metrics = results.calculate_metrics()
        
        assert metrics.total_trades == 0
        assert metrics.winning_trades == 0
        assert metrics.losing_trades == 0
        assert metrics.total_return == Decimal("500.0")
        assert metrics.duration_days == 30

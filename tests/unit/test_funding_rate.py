"""
Unit tests for funding rate data components.
"""

import pytest
from decimal import Decimal
from datetime import datetime, timezone

from nautilus_trader.model.identifiers import InstrumentId

from src.data.funding_rate import FundingRateData, FundingRateProvider


class TestFundingRateData:
    """Test FundingRateData class."""
    
    def test_funding_rate_data_creation(self):
        """Test creating FundingRateData instance."""
        instrument_id = InstrumentId.from_str("BTCUSDT-PERP.BINANCE")
        funding_rate = Decimal("0.0001")
        funding_time = 1640995200000  # 2022-01-01 00:00:00 UTC
        mark_price = Decimal("47000.0")
        ts_event = 1640995200000000000
        ts_init = 1640995200000000000
        
        data = FundingRateData(
            instrument_id=instrument_id,
            funding_rate=funding_rate,
            funding_time=funding_time,
            mark_price=mark_price,
            ts_event=ts_event,
            ts_init=ts_init,
        )
        
        assert data.instrument_id == instrument_id
        assert data.funding_rate == funding_rate
        assert data.funding_time == funding_time
        assert data.mark_price == mark_price
        assert data.ts_event == ts_event
        assert data.ts_init == ts_init
    
    def test_funding_rate_data_serialization(self):
        """Test serialization and deserialization."""
        instrument_id = InstrumentId.from_str("ETHUSDT-PERP.BINANCE")
        funding_rate = Decimal("0.0002")
        funding_time = 1640995200000
        mark_price = Decimal("3500.0")
        
        data = FundingRateData(
            instrument_id=instrument_id,
            funding_rate=funding_rate,
            funding_time=funding_time,
            mark_price=mark_price,
            ts_event=1640995200000000000,
            ts_init=1640995200000000000,
        )
        
        # Test to_dict
        data_dict = data.to_dict()
        assert data_dict["instrument_id"] == str(instrument_id)
        assert data_dict["funding_rate"] == float(funding_rate)
        assert data_dict["funding_time"] == funding_time
        assert data_dict["mark_price"] == float(mark_price)
        
        # Test from_dict
        restored_data = FundingRateData.from_dict(data_dict)
        assert restored_data.instrument_id == instrument_id
        assert restored_data.funding_rate == funding_rate
        assert restored_data.funding_time == funding_time
        assert restored_data.mark_price == mark_price
    
    def test_funding_rate_data_repr(self):
        """Test string representation."""
        instrument_id = InstrumentId.from_str("BTCUSDT-PERP.BINANCE")
        data = FundingRateData(
            instrument_id=instrument_id,
            funding_rate=Decimal("0.0001"),
            funding_time=1640995200000,
            mark_price=Decimal("47000.0"),
        )
        
        repr_str = repr(data)
        assert "FundingRateData" in repr_str
        assert "BTCUSDT-PERP.BINANCE" in repr_str
        assert "0.0001" in repr_str


class TestFundingRateProvider:
    """Test FundingRateProvider class."""
    
    @pytest.fixture
    def mock_client(self):
        """Mock Binance futures HTTP client."""
        class MockClient:
            async def query_premium_index(self, symbol):
                return {
                    "symbol": symbol,
                    "markPrice": "47000.0",
                    "indexPrice": "46995.0",
                    "lastFundingRate": "0.0001",
                    "nextFundingTime": 1640995200000,
                    "time": 1640995200000,
                }
            
            async def query_funding_rate_history(self, symbol, start_time=None, end_time=None, limit=100):
                return [
                    {
                        "symbol": symbol,
                        "fundingRate": "0.0001",
                        "fundingTime": 1640995200000,
                    },
                    {
                        "symbol": symbol,
                        "fundingRate": "0.0002",
                        "fundingTime": 1640995200000 + 8 * 3600 * 1000,  # 8 hours later
                    },
                ]
        
        return MockClient()
    
    def test_funding_rate_provider_creation(self, mock_client):
        """Test creating FundingRateProvider."""
        instruments = ["BTCUSDT", "ETHUSDT"]
        provider = FundingRateProvider(mock_client, instruments)
        
        assert provider._client == mock_client
        assert provider._instruments == instruments
        assert provider._funding_rates == {}
    
    @pytest.mark.asyncio
    async def test_get_current_funding_rates(self, mock_client):
        """Test getting current funding rates."""
        instruments = ["BTCUSDT"]
        provider = FundingRateProvider(mock_client, instruments)
        
        funding_rates = await provider.get_current_funding_rates()
        
        assert "BTCUSDT" in funding_rates
        data = funding_rates["BTCUSDT"]
        assert isinstance(data, FundingRateData)
        assert data.funding_rate == Decimal("0.0001")
        assert data.mark_price == Decimal("47000.0")
    
    @pytest.mark.asyncio
    async def test_get_funding_rate_history(self, mock_client):
        """Test getting funding rate history."""
        instruments = ["BTCUSDT"]
        provider = FundingRateProvider(mock_client, instruments)
        
        history = await provider.get_funding_rate_history("BTCUSDT")
        
        assert len(history) == 2
        assert all(isinstance(data, FundingRateData) for data in history)
        assert history[0].funding_rate == Decimal("0.0001")
        assert history[1].funding_rate == Decimal("0.0002")
    
    def test_calculate_funding_cost(self, mock_client):
        """Test funding cost calculation."""
        instruments = ["BTCUSDT"]
        provider = FundingRateProvider(mock_client, instruments)
        
        # Add cached funding rate
        instrument_id = InstrumentId.from_str("BTCUSDT-PERP.BINANCE")
        funding_data = FundingRateData(
            instrument_id=instrument_id,
            funding_rate=Decimal("0.0001"),
            funding_time=1640995200000,
            mark_price=Decimal("47000.0"),
        )
        provider._funding_rates["BTCUSDT"] = funding_data
        
        # Calculate funding cost
        cost = provider.calculate_funding_cost(
            symbol="BTCUSDT",
            position_size=Decimal("1.0"),
            holding_hours=8,
        )
        
        expected_cost = Decimal("1.0") * Decimal("47000.0") * Decimal("0.0001") * Decimal("1.0")
        assert cost == expected_cost
    
    def test_is_funding_profitable(self, mock_client):
        """Test funding profitability check."""
        instruments = ["BTCUSDT"]
        provider = FundingRateProvider(mock_client, instruments)
        
        # Test positive funding rate
        instrument_id = InstrumentId.from_str("BTCUSDT-PERP.BINANCE")
        funding_data = FundingRateData(
            instrument_id=instrument_id,
            funding_rate=Decimal("0.002"),  # 0.2%
            funding_time=1640995200000,
        )
        provider._funding_rates["BTCUSDT"] = funding_data
        
        # Short position should be profitable with positive funding
        assert provider.is_funding_profitable("BTCUSDT", "short", Decimal("0.001"))
        assert not provider.is_funding_profitable("BTCUSDT", "long", Decimal("0.001"))
        
        # Test negative funding rate
        funding_data.funding_rate = Decimal("-0.002")  # -0.2%
        provider._funding_rates["BTCUSDT"] = funding_data
        
        # Long position should be profitable with negative funding
        assert provider.is_funding_profitable("BTCUSDT", "long", Decimal("0.001"))
        assert not provider.is_funding_profitable("BTCUSDT", "short", Decimal("0.001"))

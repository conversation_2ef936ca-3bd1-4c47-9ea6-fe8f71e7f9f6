"""
Pytest configuration and fixtures for the funding rate arbitrage system tests.
"""

import pytest
from decimal import Decimal
from pathlib import Path

from src.config import Config, BinanceConfig, RiskConfig, StrategyConfig


@pytest.fixture
def test_config():
    """Create a test configuration."""
    return Config(
        environment="test",
        debug=True,
        trader_id="TEST_TRADER",
        instance_id="TEST_001",
        binance=BinanceConfig(
            api_key="test_key",
            api_secret="test_secret",
            testnet=True,
        ),
        risk=RiskConfig(
            max_position_size=Decimal("100.0"),
            max_portfolio_exposure=Decimal("1000.0"),
            min_funding_rate_threshold=Decimal("0.01"),
        ),
        strategy=StrategyConfig(
            instruments=["BTCUSDT", "ETHUSDT"],
            funding_rate_check_interval=60,
        ),
    )


@pytest.fixture
def test_data_dir(tmp_path):
    """Create a temporary data directory for tests."""
    data_dir = tmp_path / "test_data"
    data_dir.mkdir()
    return data_dir


@pytest.fixture
def mock_binance_api():
    """Mock Binance API responses."""
    # TODO: Implement mock API responses
    pass


@pytest.fixture
def sample_funding_rate_data():
    """Sample funding rate data for testing."""
    return {
        "BTCUSDT": {
            "symbol": "BTCUSDT",
            "fundingRate": "0.0001",
            "fundingTime": 1640995200000,  # 2022-01-01 00:00:00 UTC
        },
        "ETHUSDT": {
            "symbol": "ETHUSDT", 
            "fundingRate": "0.0002",
            "fundingTime": 1640995200000,
        },
    }


@pytest.fixture
def sample_market_data():
    """Sample market data for testing."""
    return {
        "spot": {
            "BTCUSDT": {
                "symbol": "BTCUSDT",
                "price": "47000.00",
                "bidPrice": "46999.00",
                "askPrice": "47001.00",
            },
        },
        "futures": {
            "BTCUSDT": {
                "symbol": "BTCUSDT",
                "price": "47010.00",
                "bidPrice": "47009.00", 
                "askPrice": "47011.00",
            },
        },
    }

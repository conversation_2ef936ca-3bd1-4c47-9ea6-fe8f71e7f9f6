#!/usr/bin/env python3
"""
Script to run backtests for the funding rate arbitrage system.
"""

import asyncio
import sys
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.config import Config
from src.backtesting.engine import BacktestEngine
from src.backtesting.results import BacktestResults
from src.utils.logging import setup_logging


async def run_simple_backtest():
    """Run a simple backtest example."""
    # Setup logging
    setup_logging(level="INFO", log_file="./logs/backtest.log")
    
    # Load configuration
    config = Config()
    
    # Backtest parameters
    symbols = ["BTCUSDT", "ETHUSDT"]
    start_date = datetime(2024, 1, 1, tzinfo=timezone.utc)
    end_date = datetime(2024, 1, 31, tzinfo=timezone.utc)  # 1 month
    start_capital = Decimal("10000.0")
    
    # Strategy configuration
    strategy_config = {
        "min_funding_rate_threshold": float(config.risk.min_funding_rate_threshold),
        "min_arbitrage_profit": float(config.strategy.min_arbitrage_profit),
        "max_spread_threshold": 0.02,  # 2%
        "position_size_percentage": 0.1,  # 10% of capital per trade
    }
    
    print("=" * 60)
    print("Funding Rate Arbitrage Backtest")
    print("=" * 60)
    print(f"Symbols: {symbols}")
    print(f"Period: {start_date.date()} to {end_date.date()}")
    print(f"Start Capital: ${start_capital:,.2f}")
    print(f"Strategy Config: {strategy_config}")
    print("=" * 60)
    
    # Create backtest engine
    engine = BacktestEngine(
        start_capital=start_capital,
        commission_rate=Decimal("0.001"),  # 0.1%
        slippage_bps=1,  # 1 basis point
    )
    
    # Run backtest
    print("Running backtest...")
    results = await engine.run_backtest(
        symbols=symbols,
        start_date=start_date,
        end_date=end_date,
        strategy_config=strategy_config,
    )
    
    # Display results
    print("\nBacktest completed!")
    print(results.get_summary_report())
    
    # Save detailed results
    trades_df = results.get_trades_dataframe()
    equity_df = results.get_equity_curve_dataframe()
    
    if not trades_df.empty:
        trades_file = "./data/backtest_trades.csv"
        trades_df.to_csv(trades_file, index=False)
        print(f"\nTrades saved to: {trades_file}")
    
    if not equity_df.empty:
        equity_file = "./data/backtest_equity.csv"
        equity_df.to_csv(equity_file, index=False)
        print(f"Equity curve saved to: {equity_file}")
    
    return results


async def run_parameter_sweep():
    """Run backtest with different parameter combinations."""
    print("Running parameter sweep...")
    
    # Parameter ranges
    funding_thresholds = [0.005, 0.01, 0.015, 0.02]  # 0.5% to 2%
    profit_thresholds = [0.002, 0.005, 0.01]  # 0.2% to 1%
    
    results_summary = []
    
    for funding_threshold in funding_thresholds:
        for profit_threshold in profit_thresholds:
            print(f"\nTesting: funding_threshold={funding_threshold:.3f}, profit_threshold={profit_threshold:.3f}")
            
            strategy_config = {
                "min_funding_rate_threshold": funding_threshold,
                "min_arbitrage_profit": profit_threshold,
                "max_spread_threshold": 0.02,
                "position_size_percentage": 0.1,
            }
            
            engine = BacktestEngine(start_capital=Decimal("10000.0"))
            
            results = await engine.run_backtest(
                symbols=["BTCUSDT"],
                start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
                end_date=datetime(2024, 1, 15, tzinfo=timezone.utc),  # Shorter period for sweep
                strategy_config=strategy_config,
            )
            
            metrics = results.calculate_metrics()
            
            results_summary.append({
                "funding_threshold": funding_threshold,
                "profit_threshold": profit_threshold,
                "total_return": float(metrics.total_return),
                "total_trades": metrics.total_trades,
                "win_rate": float(metrics.win_rate),
                "sharpe_ratio": float(metrics.sharpe_ratio),
                "max_drawdown": float(metrics.max_drawdown),
            })
            
            print(f"  Return: ${metrics.total_return:,.2f}, Trades: {metrics.total_trades}, Win Rate: {metrics.win_rate:.2f}%")
    
    # Find best parameters
    best_result = max(results_summary, key=lambda x: x["total_return"])
    
    print("\n" + "=" * 60)
    print("Parameter Sweep Results")
    print("=" * 60)
    print("Best Parameters:")
    print(f"  Funding Threshold: {best_result['funding_threshold']:.3f}")
    print(f"  Profit Threshold: {best_result['profit_threshold']:.3f}")
    print(f"  Total Return: ${best_result['total_return']:,.2f}")
    print(f"  Total Trades: {best_result['total_trades']}")
    print(f"  Win Rate: {best_result['win_rate']:.2f}%")
    print(f"  Sharpe Ratio: {best_result['sharpe_ratio']:.2f}")
    print(f"  Max Drawdown: {best_result['max_drawdown']:.2f}%")
    
    # Save parameter sweep results
    import pandas as pd
    sweep_df = pd.DataFrame(results_summary)
    sweep_file = "./data/parameter_sweep.csv"
    sweep_df.to_csv(sweep_file, index=False)
    print(f"\nParameter sweep results saved to: {sweep_file}")


async def run_multi_timeframe_backtest():
    """Run backtest across multiple timeframes."""
    print("Running multi-timeframe backtest...")
    
    timeframes = [
        ("1 Week", datetime(2024, 1, 1), datetime(2024, 1, 8)),
        ("1 Month", datetime(2024, 1, 1), datetime(2024, 2, 1)),
        ("3 Months", datetime(2024, 1, 1), datetime(2024, 4, 1)),
    ]
    
    strategy_config = {
        "min_funding_rate_threshold": 0.01,
        "min_arbitrage_profit": 0.005,
        "max_spread_threshold": 0.02,
        "position_size_percentage": 0.1,
    }
    
    print("\nTimeframe Analysis:")
    print("-" * 40)
    
    for name, start_date, end_date in timeframes:
        start_date = start_date.replace(tzinfo=timezone.utc)
        end_date = end_date.replace(tzinfo=timezone.utc)
        
        engine = BacktestEngine(start_capital=Decimal("10000.0"))
        
        results = await engine.run_backtest(
            symbols=["BTCUSDT"],
            start_date=start_date,
            end_date=end_date,
            strategy_config=strategy_config,
        )
        
        metrics = results.calculate_metrics()
        
        print(f"{name}:")
        print(f"  Period: {start_date.date()} to {end_date.date()}")
        print(f"  Return: ${metrics.total_return:,.2f} ({metrics.total_return_percentage:.2f}%)")
        print(f"  Annualized: {metrics.annualized_return:.2f}%")
        print(f"  Trades: {metrics.total_trades}")
        print(f"  Win Rate: {metrics.win_rate:.2f}%")
        print(f"  Sharpe: {metrics.sharpe_ratio:.2f}")
        print(f"  Max DD: {metrics.max_drawdown:.2f}%")
        print()


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run funding rate arbitrage backtests")
    parser.add_argument(
        "--mode",
        choices=["simple", "sweep", "timeframe", "all"],
        default="simple",
        help="Backtest mode to run"
    )
    
    args = parser.parse_args()
    
    if args.mode == "simple":
        asyncio.run(run_simple_backtest())
    elif args.mode == "sweep":
        asyncio.run(run_parameter_sweep())
    elif args.mode == "timeframe":
        asyncio.run(run_multi_timeframe_backtest())
    elif args.mode == "all":
        asyncio.run(run_simple_backtest())
        asyncio.run(run_parameter_sweep())
        asyncio.run(run_multi_timeframe_backtest())


if __name__ == "__main__":
    main()

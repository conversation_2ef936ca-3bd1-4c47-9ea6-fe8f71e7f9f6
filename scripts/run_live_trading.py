#!/usr/bin/env python3
"""
Script to run live trading for the funding rate arbitrage system.
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.config import Config
from src.live.trading_node import LiveTradingNode
from src.utils.logging import setup_logging, TradingLogger


class LiveTradingRunner:
    """Runner for live trading system."""
    
    def __init__(
        self,
        config_path: Optional[str] = None,
        environment: str = "development",
    ) -> None:
        self._config_path = config_path
        self._environment = environment
        self._trading_node: Optional[LiveTradingNode] = None
        self._is_running = False
        self._logger = TradingLogger("live_trading_runner")
    
    async def start(self) -> None:
        """Start the live trading system."""
        try:
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Create and start trading node
            self._trading_node = LiveTradingNode(
                config_path=self._config_path,
                environment=self._environment,
            )
            
            await self._trading_node.start()
            self._is_running = True
            
            self._logger.logger.info("Live trading system started successfully")
            
            # Keep running until stopped
            await self._run_forever()
            
        except KeyboardInterrupt:
            self._logger.logger.info("Received keyboard interrupt")
        except Exception as e:
            self._logger.logger.error(f"Error in live trading system: {e}")
            raise
        finally:
            await self._shutdown()
    
    async def _run_forever(self) -> None:
        """Keep the system running."""
        while self._is_running:
            try:
                # Log system status periodically
                if self._trading_node:
                    status = self._trading_node.get_status()
                    self._logger.logger.info(
                        "System status",
                        portfolio_value=status.get("portfolio_value", 0),
                        active_strategies=len(status.get("strategies", [])),
                    )
                
                # Wait before next status update
                await asyncio.sleep(300)  # 5 minutes
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self._logger.logger.error(f"Error in main loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _shutdown(self) -> None:
        """Shutdown the trading system gracefully."""
        self._logger.logger.info("Shutting down live trading system")
        
        self._is_running = False
        
        if self._trading_node:
            await self._trading_node.stop()
        
        self._logger.logger.info("Live trading system shutdown complete")
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            self._logger.logger.info(f"Received signal {signum}")
            self._is_running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def emergency_stop(self) -> None:
        """Emergency stop the trading system."""
        self._logger.logger.critical("EMERGENCY STOP INITIATED")
        
        if self._trading_node:
            await self._trading_node.emergency_stop()
        
        await self._shutdown()


async def run_live_trading(
    config_path: Optional[str] = None,
    environment: str = "development",
) -> None:
    """Run live trading with the specified configuration."""
    # Load configuration to setup logging
    if config_path:
        config = Config.from_yaml(Path(config_path))
    else:
        config = Config()
    
    # Setup logging
    setup_logging(
        level=config.logging.level,
        log_file=config.logging.file_path,
        max_file_size=config.logging.max_file_size,
        backup_count=config.logging.backup_count,
    )
    
    logger = TradingLogger("main")
    
    # Validate configuration
    if not config.binance.api_key or not config.binance.api_secret:
        logger.logger.error("Binance API credentials not configured")
        return
    
    if not config.strategy.instruments:
        logger.logger.error("No instruments configured for trading")
        return
    
    logger.logger.info(
        f"Starting live trading in {environment} mode",
        trader_id=config.trader_id,
        instruments=config.strategy.instruments,
        testnet=config.binance.testnet,
    )
    
    # Create and run trading system
    runner = LiveTradingRunner(config_path, environment)
    await runner.start()


async def run_paper_trading() -> None:
    """Run paper trading mode."""
    print("Starting paper trading mode...")
    
    # Create a test configuration for paper trading
    config = Config()
    config.binance.testnet = True  # Use testnet for paper trading
    
    # Save temporary config
    temp_config_path = "./config/paper_trading.yaml"
    config.to_yaml(Path(temp_config_path))
    
    await run_live_trading(temp_config_path, "paper_trading")


async def run_system_check() -> None:
    """Run system health check."""
    print("Running system health check...")
    
    try:
        # Load configuration
        config = Config()
        
        # Check configuration
        print("✓ Configuration loaded")
        
        # Check API credentials
        if config.binance.api_key and config.binance.api_secret:
            print("✓ API credentials configured")
        else:
            print("✗ API credentials missing")
            return
        
        # Check instruments
        if config.strategy.instruments:
            print(f"✓ {len(config.strategy.instruments)} instruments configured")
        else:
            print("✗ No instruments configured")
            return
        
        # Test trading node creation
        trading_node = LiveTradingNode(environment="test")
        print("✓ Trading node created successfully")
        
        # Test component initialization (without starting)
        await trading_node._initialize_components()
        print("✓ Components initialized successfully")
        
        print("\nSystem health check passed! ✓")
        
    except Exception as e:
        print(f"\nSystem health check failed: {e} ✗")


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run live trading system")
    parser.add_argument(
        "--config",
        type=str,
        help="Path to configuration file"
    )
    parser.add_argument(
        "--environment",
        choices=["development", "staging", "production", "paper_trading"],
        default="development",
        help="Trading environment"
    )
    parser.add_argument(
        "--mode",
        choices=["live", "paper", "check"],
        default="live",
        help="Run mode"
    )
    
    args = parser.parse_args()
    
    if args.mode == "live":
        asyncio.run(run_live_trading(args.config, args.environment))
    elif args.mode == "paper":
        asyncio.run(run_paper_trading())
    elif args.mode == "check":
        asyncio.run(run_system_check())


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Setup script for the funding rate arbitrage system.
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path


def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command."""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"Error running command: {command}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        sys.exit(1)
    
    return result


def check_python_version():
    """Check if Python version is 3.11+."""
    if sys.version_info < (3, 11):
        print("Error: Python 3.11 or higher is required")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def create_directories():
    """Create necessary directories."""
    directories = [
        "data/catalog",
        "logs",
        "config",
        "notebooks",
        "tests/unit",
        "tests/integration",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")


def copy_config_files():
    """Copy example configuration files."""
    config_files = [
        (".env.example", ".env"),
        ("config/config.example.yaml", "config/config.yaml"),
    ]
    
    for src, dst in config_files:
        if not Path(dst).exists():
            shutil.copy2(src, dst)
            print(f"✓ Copied {src} to {dst}")
        else:
            print(f"! {dst} already exists, skipping")


def install_dependencies():
    """Install Python dependencies."""
    print("Installing dependencies...")
    
    # Upgrade pip
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # Install requirements
    run_command(f"{sys.executable} -m pip install -r requirements.txt")
    
    print("✓ Dependencies installed")


def setup_git_hooks():
    """Set up git hooks for code quality."""
    hooks_dir = Path(".git/hooks")
    if hooks_dir.exists():
        pre_commit_hook = hooks_dir / "pre-commit"
        
        hook_content = """#!/bin/bash
# Pre-commit hook for code quality checks

echo "Running code quality checks..."

# Format code with black
black src/ tests/ --check --quiet
if [ $? -ne 0 ]; then
    echo "Code formatting issues found. Run 'black src/ tests/' to fix."
    exit 1
fi

# Lint with flake8
flake8 src/ tests/
if [ $? -ne 0 ]; then
    echo "Linting issues found."
    exit 1
fi

# Type check with mypy
mypy src/
if [ $? -ne 0 ]; then
    echo "Type checking issues found."
    exit 1
fi

echo "All checks passed!"
"""
        
        with open(pre_commit_hook, "w") as f:
            f.write(hook_content)
        
        os.chmod(pre_commit_hook, 0o755)
        print("✓ Git pre-commit hook installed")


def main():
    """Main setup function."""
    print("Setting up Funding Rate Arbitrage System...")
    print("=" * 50)
    
    check_python_version()
    create_directories()
    copy_config_files()
    install_dependencies()
    setup_git_hooks()
    
    print("\n" + "=" * 50)
    print("Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env and config/config.yaml with your API credentials")
    print("2. Run 'python -m src.cli validate-config' to check configuration")
    print("3. Run 'python -m src.cli status' to see system status")
    print("4. Start with paper trading: 'python -m src.cli paper-trade'")


if __name__ == "__main__":
    main()
